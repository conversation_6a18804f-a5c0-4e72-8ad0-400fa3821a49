<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_result_user-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="user_id" property="userId" />
    <result column="xpd_level_id" property="xpdLevelId" />
    <result column="competent" property="competent" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="score_value" property="scoreValue" />
    <result column="qualified_ptg" property="qualifiedPtg" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, user_id, xpd_level_id, competent, deleted, create_user_id, create_time, 
    update_user_id, update_time, calc_batch_no, score_value, qualified_ptg, cali_flag,
    original_snap
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_result_user
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_result_user
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user (id, org_id, xpd_id, user_id, xpd_level_id, competent, deleted, 
      create_user_id, create_time, update_user_id, update_time, calc_batch_no, 
      score_value, qualified_ptg, cali_flag, original_snap)
    values (#{id}, #{orgId}, #{xpdId}, #{userId}, #{xpdLevelId}, #{competent}, #{deleted}, 
      #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{calcBatchNo}, 
      #{scoreValue}, #{qualifiedPtg}, #{caliFlag}, #{originalSnap})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO">
    <!--@mbg.generated-->
    update rv_xpd_result_user
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      user_id = #{userId},
      xpd_level_id = #{xpdLevelId},
      competent = #{competent},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calc_batch_no = #{calcBatchNo},
      score_value = #{scoreValue},
      qualified_ptg = #{qualifiedPtg},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_result_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="xpd_level_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdLevelId}
        </foreach>
      </trim>
      <trim prefix="competent = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.competent}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreValue}
        </foreach>
      </trim>
      <trim prefix="qualified_ptg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.qualifiedPtg}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user
    (id, org_id, xpd_id, user_id, xpd_level_id, competent, deleted, create_user_id, create_time, 
      update_user_id, update_time, calc_batch_no, score_value, qualified_ptg, cali_flag,
      original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.xpdLevelId}, #{item.competent},
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId},
        #{item.updateTime}, #{item.calcBatchNo}, #{item.scoreValue}, #{item.qualifiedPtg},
        #{item.caliFlag}, #{item.originalSnap})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user
    (id, org_id, xpd_id, user_id, xpd_level_id, competent, deleted, create_user_id, create_time,
      update_user_id, update_time, calc_batch_no, score_value, qualified_ptg, cali_flag,
      original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.userId}, #{item.xpdLevelId}, #{item.competent}, 
        #{item.deleted}, #{item.createUserId}, #{item.createTime}, #{item.updateUserId}, 
        #{item.updateTime}, #{item.calcBatchNo}, #{item.scoreValue}, #{item.qualifiedPtg},
        #{item.caliFlag}, #{item.originalSnap})
    </foreach>
    on duplicate key update
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    user_id=values(user_id),
    xpd_level_id=values(xpd_level_id),
    competent=values(competent),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calc_batch_no=values(calc_batch_no),
    score_value=values(score_value),
    qualified_ptg=values(qualified_ptg),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user
    (id, org_id, xpd_id, user_id, xpd_level_id, competent, deleted, create_user_id, create_time, 
      update_user_id, update_time, calc_batch_no, score_value, qualified_ptg, cali_flag,
      original_snap)
    values
    (#{id}, #{orgId}, #{xpdId}, #{userId}, #{xpdLevelId}, #{competent}, #{deleted}, #{createUserId}, 
      #{createTime}, #{updateUserId}, #{updateTime}, #{calcBatchNo}, #{scoreValue}, #{qualifiedPtg},
      #{caliFlag}, #{originalSnap})
    on duplicate key update
    id = #{id},
    org_id = #{orgId},
    xpd_id = #{xpdId},
    user_id = #{userId},
    xpd_level_id = #{xpdLevelId},
    competent = #{competent},
    deleted = #{deleted},
    create_user_id = #{createUserId},
    create_time = #{createTime},
    update_user_id = #{updateUserId},
    update_time = #{updateTime},
    calc_batch_no = #{calcBatchNo},
    score_value = #{scoreValue},
    qualified_ptg = #{qualifiedPtg},
    cali_flag = #{caliFlag},
    original_snap = #{originalSnap}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="xpdLevelId != null">
        xpd_level_id,
      </if>
      <if test="competent != null">
        competent,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="xpdLevelId != null">
        #{xpdLevelId},
      </if>
      <if test="competent != null">
        #{competent},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        #{qualifiedPtg},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="xpdLevelId != null">
        xpd_level_id = #{xpdLevelId},
      </if>
      <if test="competent != null">
        competent = #{competent},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="qualifiedPtg != null">
        qualified_ptg = #{qualifiedPtg},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
    </trim>
  </insert>

  <select id="queryIgnoreDelByXpdId" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO">
    select id,user_id from rv_xpd_result_user where org_id = #{orgId}
    and xpd_id = #{xpdId} and user_id in
    <foreach close=")" collection="userIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="batchUpdateResult">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_result_user set
      xpd_level_id = #{item.xpdLevelId},
      competent = #{item.competent},
      score_value = #{item.scoreValue},
      qualified_ptg = #{item.qualifiedPtg},
      calc_batch_no = #{item.calcBatchNo},
      deleted = #{item.deleted},
      update_user_id = #{item.updateUserId},
      update_time = #{item.updateTime} where id = #{item.id}
    </foreach>
  </update>

  <select id="listSortValue" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.DecimalPtgBean">
    select id,
    <choose>
      <when test="getScore">
        score_value as sort_value
      </when>
      <otherwise>
        <!--@ignoreSql-->
        qualified_ptg as sort_value
      </otherwise>
    </choose>
    from rv_xpd_result_user
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
  </select>

  <update id="batchUpdateLevel">
    <foreach collection="list" item="item" separator=";">
      update rv_xpd_result_user set xpd_level_id = #{item.levelId},competent = #{item.competent} where id = #{item.id}
    </foreach>
  </update>

   <select id="selectLevelAgg" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdLevelAggVO">
    select b.id                      as levelId
         , b.level_name              as levelName
         , b.level_name_i18n         as levelNameI18n
         , b.order_index
         , count(distinct a.user_id) as userCnt
    from rv_xpd_level            b

    left join rv_xpd_result_user a
              on a.xpd_level_id = b.id and a.org_id = b.org_id and b.deleted = 0 and a.xpd_id = b.xpd_id

    left join udp_lite_user_sp   c on c.org_id = a.org_id and c.user_id = a.user_id

    and exists(
      select 1
      from rv_activity_participation_member d
      join rv_xpd                           e on e.org_id = d.org_id and e.aom_prj_id = d.actv_id and e.deleted = 0
      where d.org_id = #{orgId}
      and e.id = #{xpdId}
      and d.deleted = 0
      and d.user_id = c.user_id
    )

    where b.org_id = #{orgId}
      and b.xpd_id = #{xpdId}
      and b.deleted = 0

    <include refid="auth_fragment" />

    <if test="query.levelIds != null and query.levelIds.size() != 0">
      and b.id in
      <foreach close=")" collection="query.levelIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    group by b.id, b.order_index
    order by b.order_index
  </select>

  <select id="findUserResultByActId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_result_user
    where org_id = #{orgId}
      and xpd_id = #{xpdId}
      and deleted = 0
  </select>

  <sql id="auth_fragment">
    <choose>
      <when test="query.emptyAuth">
        <!--@ignoreSql-->
        and 1 != 1
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0) and (query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        <!--@ignoreSql-->
        and (c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        or c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0)">
        and c.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="(query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <choose>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '') and (query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and (
          (
            <if test="query.kwType != null and query.kwType == 2">
              c.username like concat('%', #{query.escapedSearchKey}, '%')
            </if>
            <if test="query.kwType != null and query.kwType == 1">
              c.fullname like concat('%', #{query.escapedSearchKey}, '%')
            </if>
            <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
              (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
            </if>
          )
          or c.id in
        <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.escapedSearchKey != null and query.escapedSearchKey != '')">
        <if test="query.kwType != null and query.kwType == 2">
          and c.username like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType != null and query.kwType == 1">
          and c.fullname like concat('%', #{query.escapedSearchKey}, '%')
        </if>
        <if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">
          and (c.username like concat('%', #{query.escapedSearchKey}, '%') or c.fullname like concat('%', #{query.escapedSearchKey}, '%'))
        </if>
      </when>
      <when test="(query.searchUserIds != null and query.searchUserIds.size() != 0)">
        and c.id in
        <foreach close=")" collection="query.searchUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>

    <if test="query.posIds != null and query.posIds.size() != 0">
      and c.position_id in
      <foreach close=")" collection="query.posIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.gradeIds != null and query.gradeIds.size() != 0">
      and c.grade_id in
      <foreach close=")" collection="query.gradeIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>

    <if test="query.userStatus != null and query.userStatus != -1">
      <if test="query.userStatus == 2">
        and c.deleted = 1
      </if>
      <if test="query.userStatus != 2">
        and c.status = #{query.userStatus}
      </if>
    </if>
  </sql>

  <select id="selectUserTableResult" resultType="com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO">
    WITH
        <!--CTE for user results with row number-->
        user_results_rn AS (
            SELECT
                rs.xpd_id,
                rs.org_id,
                rs.user_id,
                rs.xpd_level_id,
                rs.score_value,
                rs.qualified_ptg,
                ROW_NUMBER() OVER (PARTITION BY rs.org_id, rs.xpd_id, rs.user_id ORDER BY rs.create_time DESC) AS rn
            FROM
                rv_xpd_result_user rs
            WHERE
                rs.org_id = #{orgId}
                AND rs.xpd_id = #{xpdId}
                AND rs.deleted = 0
        ),

    <!--CTE for user results, filtering by row number = 1-->
        user_results_filtered AS (
            SELECT *
            FROM user_results_rn
            WHERE rn = 1
        ),

    <!--CTE for user dimension results-->
        user_dim_results AS (
            SELECT
                aa.org_id,
                aa.xpd_id,
                aa.user_id,
                bb.result_type,
                aa.xpd_level_id AS levelId,
                oo.order_index AS levelIndex,
                oo.level_name AS levelName,
                oo.level_name_i18n AS levelNameI18n,
                aa.score_value,
                aa.qualified_ptg,
                CASE
                    WHEN bb.result_type = 0 THEN '-'
                    WHEN bb.result_type = 1 THEN FORMAT(aa.score_value, 2)
                    WHEN bb.result_type = 2 THEN CONCAT(FORMAT(aa.qualified_ptg, 2), '%')
                END AS result
            FROM
                user_results_filtered aa
                JOIN rv_xpd_rule bb ON bb.org_id = aa.org_id AND bb.xpd_id = aa.xpd_id AND bb.deleted = 0
                JOIN rv_xpd_level oo ON oo.id = aa.xpd_level_id AND oo.xpd_id = aa.xpd_id AND oo.deleted = 0
            WHERE
                aa.xpd_id = #{xpdId}
        )

        <if test="query.includeSub">
        ,
        <!--CTE for sub-dimension results with row number-->
        subdim_results_rn AS (
            SELECT
                rs.xpd_id,
                rs.org_id,
                rs.user_id,
                rs.sd_dim_id,
                rs.score_value,
                rs.qualified_ptg,
                rs.perf_result_id,
                ROW_NUMBER() OVER (PARTITION BY rs.org_id, rs.xpd_id, rs.user_id, rs.sd_dim_id ORDER BY rs.create_time DESC) AS rn
            FROM
                rv_xpd_result_user_dim rs
            WHERE
                rs.org_id = #{orgId}
                AND rs.xpd_id = #{xpdId}
                AND rs.deleted = 0
        ),
        <!--CTE for sub-dimension results, filtering by row number = 1-->
        subdim_results_filtered AS (
            SELECT *
            FROM subdim_results_rn
            WHERE rn = 1
        ),
        <!--CTE for sub-dimension results with calculations-->
        subdim_results AS (
            SELECT
                m.user_id,
                m.sd_dim_id,
                n.result_type,
                CASE
                    WHEN m.perf_result_id != '' THEN m.perf_result_id
                    <!-- 按照维度导入结果时，没有维度规则数据，也没有指标和达标率 -->
                    WHEN n.id IS NULL THEN ''
                    WHEN n.result_type = 0 THEN FORMAT(m.score_value, 2)
                    WHEN n.result_type = 1 THEN CONCAT(FORMAT(m.qualified_ptg, 2), '%')
                END AS subResult
            FROM
                subdim_results_filtered m
                LEFT JOIN rv_xpd_dim_rule n ON n.xpd_id = m.xpd_id AND n.sd_dim_id = m.sd_dim_id AND n.deleted = 0
            WHERE
                m.xpd_id = #{xpdId}
        )
        </if>

    SELECT
        c.user_id,
        c.fullname,
        c.username,
        IF(c.deleted = 1, 2, c.status) AS status,
        c.dept_name,
        c.dept_id,
        c.position_id,
        c.position_name,
        c.grade_id,
        c.grade_name,
        d.levelName,
        d.levelNameI18n,
        d.result
        <if test="query.includeSub">
        , f.sd_dim_id AS subId
        , '维度名称需要跨人才标准的库查询' AS subName
        , e.subResult
        </if>
    FROM
        rv_activity_participation_member a
        JOIN rv_xpd b ON b.aom_prj_id = a.actv_id AND b.deleted = 0 AND a.org_id = b.org_id
        JOIN udp_lite_user_sp c ON c.user_id = a.user_id AND a.org_id = c.org_id
        LEFT JOIN user_dim_results d ON a.user_id = d.user_id AND d.xpd_id = b.id
        <if test="query.includeSub">
        LEFT JOIN rv_xpd_dim f ON f.xpd_id = b.id AND f.deleted = 0
        LEFT JOIN subdim_results e ON e.user_id = d.user_id AND e.sd_dim_id = f.sd_dim_id
        </if>
    WHERE
        a.deleted = 0
        AND b.org_id = #{orgId}
        AND b.id = #{xpdId}
        <if test="query.levelIds != null and query.levelIds.size() != 0">
        AND d.levelId IN
            <foreach close=")" collection="query.levelIds" item="levelId" open="(" separator=",">
                #{levelId}
            </foreach>
        </if>
        <include refid="auth_fragment" />
    ORDER BY
        IF(d.levelIndex IS NULL, 1, 0),
        d.levelIndex DESC,
        IF(d.result_type IS NULL, 1, 0),
        IF(IF(d.result_type = 0, d.score_value, d.qualified_ptg) IS NULL, 1, 0),
        IF(d.result_type = 0, d.score_value, d.qualified_ptg) DESC
        <if test="query.includeSub">
        , IF(f.sd_dim_id IS NULL, 1, 0),
        f.sd_dim_id
        </if>
        , d.user_id
  </select>

  <select id="selectDeptResult" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdDeptUserDimResultDTO">
    WITH dept_user_cnt AS (
        SELECT h.id AS deptId
             , h.name AS deptName
             , h.parent_id AS parentId
             , COUNT(DISTINCT k.user_id) AS userCnt
        FROM udp_dept h
        JOIN udp_dept_relation i ON h.id = i.parent_id AND i.dept_type = 1 AND h.org_id = i.org_id
        JOIN udp_lite_user_sp c ON c.dept_id = i.dept_id AND c.org_id = h.org_id AND c.org_id = #{orgId}
        JOIN rv_activity_participation_member k ON k.user_id = c.user_id AND k.deleted = 0
        JOIN rv_xpd l ON l.org_id = k.org_id AND l.aom_prj_id = k.actv_id AND l.id = #{xpdId} AND l.deleted = 0
        WHERE h.org_id = #{orgId}
          AND h.deleted = 0

        <include refid="auth_fragment" />

        GROUP BY h.id, h.name, h.parent_id
        HAVING COUNT(DISTINCT k.user_id) &gt; 0
    ),
    dept_level_user_cnt AS (
        SELECT a.id AS deptId
             , y.id AS levelId
             , y.level_name AS levelName
             , y.level_name_i18n AS levelNameI18n
             , y.order_index AS levelOrderIndex
             , COUNT(DISTINCT x.user_id) AS levelUserCnt
        FROM udp_dept a
        JOIN udp_dept_relation b ON a.id = b.parent_id AND b.dept_type = 1 AND a.org_id = b.org_id
        JOIN udp_lite_user_sp c ON c.dept_id = b.dept_id AND c.org_id = a.org_id AND c.org_id = #{orgId}
        JOIN rv_xpd_level y ON y.org_id = a.org_id AND y.xpd_id = #{xpdId} AND y.deleted = 0
        LEFT JOIN rv_xpd_result_user x
               ON x.user_id = c.user_id AND x.xpd_level_id = y.id AND x.xpd_id = #{xpdId} AND x.deleted = 0
        WHERE a.org_id = #{orgId}
          AND a.deleted = 0
          AND EXISTS (
            SELECT 1 FROM rv_activity_participation_member f
            JOIN rv_xpd g ON g.org_id = f.org_id AND g.aom_prj_id = f.actv_id AND g.deleted = 0
            WHERE f.org_id = #{orgId}
              AND g.id = #{xpdId}
              AND f.deleted = 0
              AND f.user_id = c.user_id
          )

        <if test="query.levelIds != null and query.levelIds.size() != 0">
          AND y.id IN
          <foreach close=")" collection="query.levelIds" item="levelId" open="(" separator=",">
            #{levelId}
          </foreach>
        </if>

        <include refid="auth_fragment" />

        GROUP BY a.id, y.id, y.level_name, y.level_name_i18n, y.order_index
    )
    SELECT e.deptId
         , e.deptName
         , e.parentId
         , e.userCnt
         , g.levelId
         , g.levelName
         , g.levelNameI18n
         , g.levelOrderIndex
         , g.levelUserCnt
    FROM dept_user_cnt e
    LEFT JOIN dept_level_user_cnt g ON g.deptId = e.deptId
  </select>

  <select id="findByUserId" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_result_user
    where xpd_id = #{xpdId}
      and deleted = 0
      and user_id = #{userId}
      and org_id = #{orgId}
  </select>

  <select id="findLevelByUserId" parameterType="java.lang.String" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdResultUserLevelDTO">
    SELECT r.user_id, l.id, l.level_name FROM rv_xpd_result_user r join rv_xpd_level l on r.xpd_level_id = l.id and r.org_id = l.org_id
    WHERE r.xpd_id = #{xpdId} and r.deleted = 0 and r.org_id = #{orgId}
    <if test="(userIds != null and userIds.size()&gt;0)">
        AND r.user_id in
        <foreach close=")" collection="userIds" item="itemId" open="(" separator=",">
            #{itemId}
        </foreach>
    </if>
  </select>

    <select id="findResultsByUserIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from rv_xpd_result_user
        where org_id = #{orgId}
          and deleted = 0
        <choose>
            <when test="userIds != null and userIds.size() &gt; 0">
                and user_id in
                <foreach close=")" collection="userIds" item="itemId" open="(" separator=",">
                    #{itemId}
                </foreach>
            </when>
            <otherwise>
              <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="findResultByXpdIdAndUserId" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdResultUserLevelDTO">
        select l.id              as id,
               l.level_name      as levelName,
               l.level_name_i18n as levelNameI18n
        from rv_xpd_result_user ru
        inner join rv_xpd_level l on l.org_id = ru.org_id and l.id = ru.xpd_level_id and l.deleted = 0
        where ru.org_id = #{orgId}
          and ru.user_id = #{userId}
          and ru.xpd_id = #{xpdId}
          and ru.deleted = 0
        order by ru.update_time desc
    </select>

    <select id="findResultsByActvIdsAndUserId" resultType="com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO">
        select x.id              as projectId,
               ru.xpd_level_id   as xpdLevelId,
               l.level_name      as levelName,
               l.level_name_i18n as levelNameI18n
        from rv_xpd x
        inner join rv_xpd_result_user ru on x.org_id = ru.org_id and x.id = ru.xpd_id and ru.deleted = 0
        inner join rv_xpd_level l on l.org_id = ru.org_id and l.id = ru.xpd_level_id and l.deleted = 0
        where ru.org_id = #{orgId}
          and ru.deleted = 0
          and ru.user_id = #{userId}
        <choose>
            <when test="actvIds != null and actvIds.size() != 0">
                and x.aom_prj_id in
                <foreach close=")" collection="actvIds" item="itemId" open="(" separator=",">
                    #{itemId}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

  <select id="selectActvUserCnt" resultType="com.yxt.talent.rv.application.xpd.common.dto.XpdUserCntDTO">
    select c.id
         , b.actv_name               as projectName
         , b.actv_status             as projectStatus
         , b.start_time
         , b.end_time
         , count(distinct a.user_id) as totalUserCnt
         , count(distinct e.user_id) as completedUserCnt
    from rv_activity_participation_member a
    join      rv_activity                 b on a.org_id = b.org_id and a.actv_id = b.id and b.deleted = 0
    join      rv_xpd                      c on b.org_id = c.org_id and c.aom_prj_id = b.id and c.deleted = 0
    join      udp_lite_user_sp            d on a.org_id = d.org_id and a.user_id = d.id and d.deleted = 0
    left join rv_xpd_result_user          e
              on e.org_id = a.org_id and e.xpd_id = c.id and e.user_id = d.id and e.deleted = 0
    where a.org_id = #{orgId}
      and a.deleted = 0
      and c.id = #{criteria.prjId}

    <if test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
      and d.dept_id in
      <foreach close=")" collection="criteria.scopeDeptIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
      and d.id in
      <foreach close=")" collection="criteria.scopeUserIds" index="index" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="(criteria.scopeUserIds == null or criteria.scopeUserIds.size() == 0) and (criteria.scopeDeptIds == null or criteria.scopeDeptIds.size() == 0)">
      <!--@ignoreSql-->
      and 1 != 1
    </if>
  </select>

  <select id="selectDeptResultGroupByLevel" resultType="com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjOverviewResultDTO">
    select l.level_name as labelName, count(distinct b.user_id) as labelCount
    from rv_xpd a
    join rv_xpd_result_user b on a.org_id = b.org_id and a.id = b.xpd_id and b.deleted = 0
    join udp_lite_user_sp c on c.org_id = b.org_id and c.id = b.user_id and c.deleted = 0
    join rv_xpd_level l on l.org_id = b.org_id and l.id = b.xpd_level_id and l.deleted = 0 and a.id = l.xpd_id
    where a.org_id = #{orgId}
      and a.deleted = 0
      and a.id = #{criteria.prjId}

    <choose>
      <when test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0 and criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
        and (c.dept_id in
        <foreach close=")" collection="criteria.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        or c.id in
        <foreach close=")" collection="criteria.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>)
      </when>
      <when test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
        and c.dept_id in
        <foreach close=")" collection="criteria.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
        and c.id in
        <foreach close=")" collection="criteria.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>

    group by l.id, l.level_name, l.order_index
    ORDER BY l.order_index
  </select>

  <select id="selectMyDeptXpdtUser" resultType="com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjUserClientVO">
    with user_xpd_result as (
      select a.user_id, a.xpd_id, a.xpd_level_id, b.level_name, b.level_name_i18n, b.order_index
      from rv_xpd_result_user a
      join rv_xpd_level b on a.xpd_level_id = b.id and a.org_id = b.org_id and b.deleted = 0
      where a.org_id = #{orgId}
        and a.deleted = 0
        and a.xpd_id = #{criteria.prjId}
    )
    select aa.user_id    as userid
         , dd.fullname
         , dd.dept_id    as deptid
         , dd.dept_name
         , dd.img_url    as imgurl
         , ee.level_name as prjresult
    from rv_activity_participation_member aa
    join      rv_activity                 bb on aa.org_id = bb.org_id and aa.actv_id = bb.id and bb.deleted = 0
    join      rv_xpd                      cc on aa.org_id = cc.org_id and aa.actv_id = cc.aom_prj_id and cc.deleted = 0 and cc.id = #{criteria.prjId}
    join      udp_lite_user_sp            dd on aa.user_id = dd.id and dd.deleted = 0 and dd.org_id = aa.org_id
    left join user_xpd_result             ee on aa.user_id = ee.user_id and ee.xpd_id = cc.id

    where aa.org_id = #{orgId}
      and aa.deleted = 0

    <if test="criteria.userStatus != null">
      and dd.status = #{criteria.userStatus}
    </if>

    <if test="(criteria.searchKey != null and criteria.searchKey != '') or (criteria.userIds != null and criteria.userIds.size() != 0)">
      and(
          <!--@ignoreSql-->
          1 = 0
        <if test="criteria.userIds != null and criteria.userIds.size() != 0">
          or dd.id in
          <foreach close=")" collection="criteria.userIds" item="id" open="(" separator=",">
            #{id}
          </foreach>
        </if>

        <if test="criteria.searchKey != null and criteria.searchKey != ''">
          or (
            dd.fullname like concat('%', #{criteria.escapedSearchKey}, '%') or dd.username like concat('%', #{criteria.escapedSearchKey}, '%')
          )
        </if>
      )
    </if>

    <choose>
      <when test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0 and criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
        and (dd.dept_id in
        <foreach close=")" collection="criteria.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        and dd.id in
        <foreach close=")" collection="criteria.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="criteria.scopeDeptIds != null and criteria.scopeDeptIds.size() != 0">
        and dd.dept_id in
        <foreach close=")" collection="criteria.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="criteria.scopeUserIds != null and criteria.scopeUserIds.size() != 0">
        and dd.id in
        <foreach close=")" collection="criteria.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>

    order by aa.user_id
  </select>

  <select id="findByXpdIdAndUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_xpd_result_user
    where xpd_id = #{xpdId}
      and org_id = #{orgId}
      and deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>
</mapper>