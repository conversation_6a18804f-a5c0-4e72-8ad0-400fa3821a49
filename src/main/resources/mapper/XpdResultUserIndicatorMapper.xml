<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdResultUserIndicatorMapper">
  <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
    <!--@mbg.generated-->
    <!--@Table rv_xpd_result_user_indicator-->
    <id column="id" property="id" />
    <result column="org_id" property="orgId" />
    <result column="xpd_id" property="xpdId" />
    <result column="sd_indicator_id" property="sdIndicatorId" />
    <result column="user_id" property="userId" />
    <result column="score_value" property="scoreValue" />
    <result column="result_detail" property="resultDetail" />
    <result column="deleted" property="deleted" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_time" property="updateTime" />
    <result column="calc_batch_no" property="calcBatchNo" />
    <result column="qualified" property="qualified" />
    <result column="perf_summary" property="perfSummary" />
    <result column="perf_result_id" property="perfResultId" />
    <result column="result_dim_id" property="resultDimId" />
    <result column="cali_flag" property="caliFlag" />
    <result column="original_snap" property="originalSnap" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, deleted, 
    create_user_id, create_time, update_user_id, update_time, calc_batch_no, qualified, 
    perf_summary, perf_result_id, result_dim_id, cali_flag, original_snap
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_indicator
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from rv_xpd_result_user_indicator
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_indicator (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, 
      deleted, create_user_id, create_time, update_user_id, update_time, calc_batch_no, 
      qualified, perf_summary, perf_result_id, result_dim_id, cali_flag, original_snap
      )
    values (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{userId}, #{scoreValue}, #{resultDetail}, 
      #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{calcBatchNo}, 
      #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDimId}, #{caliFlag}, #{originalSnap}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
    <!--@mbg.generated-->
    update rv_xpd_result_user_indicator
    set org_id = #{orgId},
      xpd_id = #{xpdId},
      sd_indicator_id = #{sdIndicatorId},
      user_id = #{userId},
      score_value = #{scoreValue},
      result_detail = #{resultDetail},
      deleted = #{deleted},
      create_user_id = #{createUserId},
      create_time = #{createTime},
      update_user_id = #{updateUserId},
      update_time = #{updateTime},
      calc_batch_no = #{calcBatchNo},
      qualified = #{qualified},
      perf_summary = #{perfSummary},
      perf_result_id = #{perfResultId},
      result_dim_id = #{resultDimId},
      cali_flag = #{caliFlag},
      original_snap = #{originalSnap}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rv_xpd_result_user_indicator
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.orgId}
        </foreach>
      </trim>
      <trim prefix="xpd_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.xpdId}
        </foreach>
      </trim>
      <trim prefix="sd_indicator_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.sdIndicatorId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
      <trim prefix="score_value = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.scoreValue}
        </foreach>
      </trim>
      <trim prefix="result_detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultDetail}
        </foreach>
      </trim>
      <trim prefix="deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deleted}
        </foreach>
      </trim>
      <trim prefix="create_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createUserId}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateUserId}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="calc_batch_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.calcBatchNo}
        </foreach>
      </trim>
      <trim prefix="qualified = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.qualified}
        </foreach>
      </trim>
      <trim prefix="perf_summary = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfSummary}
        </foreach>
      </trim>
      <trim prefix="perf_result_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.perfResultId}
        </foreach>
      </trim>
      <trim prefix="result_dim_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.resultDimId}
        </foreach>
      </trim>
      <trim prefix="cali_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.caliFlag}
        </foreach>
      </trim>
      <trim prefix="original_snap = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.originalSnap}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, deleted, 
      create_user_id, create_time, update_user_id, update_time, calc_batch_no, qualified, 
      perf_summary, perf_result_id, result_dim_id, cali_flag, original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.sdIndicatorId}, #{item.userId}, 
        #{item.scoreValue}, #{item.resultDetail}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calcBatchNo}, 
        #{item.qualified}, #{item.perfSummary}, #{item.perfResultId}, #{item.resultDimId}, 
        #{item.caliFlag}, #{item.originalSnap})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" parameterType="map">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, deleted, 
      create_user_id, create_time, update_user_id, update_time, calc_batch_no, qualified, 
      perf_summary, perf_result_id, result_dim_id, cali_flag, original_snap)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.orgId}, #{item.xpdId}, #{item.sdIndicatorId}, #{item.userId}, 
        #{item.scoreValue}, #{item.resultDetail}, #{item.deleted}, #{item.createUserId}, 
        #{item.createTime}, #{item.updateUserId}, #{item.updateTime}, #{item.calcBatchNo}, 
        #{item.qualified}, #{item.perfSummary}, #{item.perfResultId}, #{item.resultDimId}, 
        #{item.caliFlag}, #{item.originalSnap})
    </foreach>
    on duplicate key update 
    id=values(id),
    org_id=values(org_id),
    xpd_id=values(xpd_id),
    sd_indicator_id=values(sd_indicator_id),
    user_id=values(user_id),
    score_value=values(score_value),
    result_detail=values(result_detail),
    deleted=values(deleted),
    create_user_id=values(create_user_id),
    create_time=values(create_time),
    update_user_id=values(update_user_id),
    update_time=values(update_time),
    calc_batch_no=values(calc_batch_no),
    qualified=values(qualified),
    perf_summary=values(perf_summary),
    perf_result_id=values(perf_result_id),
    result_dim_id=values(result_dim_id),
    cali_flag=values(cali_flag),
    original_snap=values(original_snap)
  </insert>
  <insert id="insertOrUpdate" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_indicator
    (id, org_id, xpd_id, sd_indicator_id, user_id, score_value, result_detail, deleted, 
      create_user_id, create_time, update_user_id, update_time, calc_batch_no, qualified, 
      perf_summary, perf_result_id, result_dim_id, cali_flag, original_snap)
    values
    (#{id}, #{orgId}, #{xpdId}, #{sdIndicatorId}, #{userId}, #{scoreValue}, #{resultDetail}, 
      #{deleted}, #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}, #{calcBatchNo}, 
      #{qualified}, #{perfSummary}, #{perfResultId}, #{resultDimId}, #{caliFlag}, #{originalSnap}
      )
    on duplicate key update 
    id = #{id}, 
    org_id = #{orgId}, 
    xpd_id = #{xpdId}, 
    sd_indicator_id = #{sdIndicatorId}, 
    user_id = #{userId}, 
    score_value = #{scoreValue}, 
    result_detail = #{resultDetail}, 
    deleted = #{deleted}, 
    create_user_id = #{createUserId}, 
    create_time = #{createTime}, 
    update_user_id = #{updateUserId}, 
    update_time = #{updateTime}, 
    calc_batch_no = #{calcBatchNo}, 
    qualified = #{qualified}, 
    perf_summary = #{perfSummary}, 
    perf_result_id = #{perfResultId}, 
    result_dim_id = #{resultDimId}, 
    cali_flag = #{caliFlag}, 
    original_snap = #{originalSnap}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdResultUserIndicatorPO">
    <!--@mbg.generated-->
    insert into rv_xpd_result_user_indicator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="xpdId != null">
        xpd_id,
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="scoreValue != null">
        score_value,
      </if>
      <if test="resultDetail != null">
        result_detail,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no,
      </if>
      <if test="qualified != null">
        qualified,
      </if>
      <if test="perfSummary != null">
        perf_summary,
      </if>
      <if test="perfResultId != null">
        perf_result_id,
      </if>
      <if test="resultDimId != null">
        result_dim_id,
      </if>
      <if test="caliFlag != null">
        cali_flag,
      </if>
      <if test="originalSnap != null">
        original_snap,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="orgId != null">
        #{orgId},
      </if>
      <if test="xpdId != null">
        #{xpdId},
      </if>
      <if test="sdIndicatorId != null">
        #{sdIndicatorId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="scoreValue != null">
        #{scoreValue},
      </if>
      <if test="resultDetail != null">
        #{resultDetail},
      </if>
      <if test="deleted != null">
        #{deleted},
      </if>
      <if test="createUserId != null">
        #{createUserId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateUserId != null">
        #{updateUserId},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        #{calcBatchNo},
      </if>
      <if test="qualified != null">
        #{qualified},
      </if>
      <if test="perfSummary != null">
        #{perfSummary},
      </if>
      <if test="perfResultId != null">
        #{perfResultId},
      </if>
      <if test="resultDimId != null">
        #{resultDimId},
      </if>
      <if test="caliFlag != null">
        #{caliFlag},
      </if>
      <if test="originalSnap != null">
        #{originalSnap},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="orgId != null">
        org_id = #{orgId},
      </if>
      <if test="xpdId != null">
        xpd_id = #{xpdId},
      </if>
      <if test="sdIndicatorId != null">
        sd_indicator_id = #{sdIndicatorId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="scoreValue != null">
        score_value = #{scoreValue},
      </if>
      <if test="resultDetail != null">
        result_detail = #{resultDetail},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="calcBatchNo != null">
        calc_batch_no = #{calcBatchNo},
      </if>
      <if test="qualified != null">
        qualified = #{qualified},
      </if>
      <if test="perfSummary != null">
        perf_summary = #{perfSummary},
      </if>
      <if test="perfResultId != null">
        perf_result_id = #{perfResultId},
      </if>
      <if test="resultDimId != null">
        result_dim_id = #{resultDimId},
      </if>
      <if test="caliFlag != null">
        cali_flag = #{caliFlag},
      </if>
      <if test="originalSnap != null">
        original_snap = #{originalSnap},
      </if>
    </trim>
  </insert>

    <select id="queryIgnoreDelByIndUserIds" resultType="com.yxt.talent.rv.infrastructure.repository.xpd.UserResultIdDTO">
        select id,user_id from rv_xpd_result_user_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
        and sd_indicator_id = #{sdIndicatorId} and user_id in
        <foreach close=")" collection="userIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findByIndicatorIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_result_user_indicator
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
        and sd_indicator_id in
        <foreach close=")" collection="indicatorIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="batchUpdateResult">
        <foreach collection="list" item="item" separator=";">
            update rv_xpd_result_user_indicator set
            result_dim_id = #{item.resultDimId},
            score_value = #{item.scoreValue},
            qualified = #{item.qualified},
            result_detail = #{item.resultDetail},
            calc_batch_no = #{item.calcBatchNo},
            perf_result_id = #{item.perfResultId},
            perf_summary = #{item.perfSummary},
            deleted = #{item.deleted},
            update_user_id = #{item.updateUserId},
            update_time = #{item.updateTime} where id = #{item.id}
        </foreach>
    </update>

    <select id="selectByXpdIdAndResultDimIdAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_result_user_indicator where org_id = #{orgId} and xpd_id = #{xpdId}
        and result_dim_id = #{resultDimId} and user_id = #{userId}
    </select>

  <select id="findByIndicatorIdsAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_indicator
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    and sd_indicator_id in
    <foreach close=")" collection="indicatorIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and user_id = #{userId}
  </select>
    <select id="findByXpdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_result_user_indicator
        where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    </select>

    <select id="findByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from rv_xpd_result_user_indicator
        where org_id = #{orgId}
          and xpd_id = #{xpdId}
          and user_id = #{userId}
          and deleted = 0
    </select>

  <select id="findByXpdIdAndUserIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from rv_xpd_result_user_indicator
    where org_id = #{orgId} and xpd_id = #{xpdId} and deleted = 0
    <choose>
      <when test="userIds != null and userIds.size() != 0">
        and user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
          #{item}
        </foreach>
      </when>
      <otherwise>
        <!--@ignoreSql-->
        and 1 != 1
      </otherwise>
    </choose>
  </select>
</mapper>