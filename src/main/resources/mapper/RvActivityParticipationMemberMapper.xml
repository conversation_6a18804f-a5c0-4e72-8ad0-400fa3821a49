<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper">
  <resultMap id="BaseResultMap"
             type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.RvActivityParticipationMemberPO">
    <!--@mbg.generated-->
    <!--@Table rv_activity_participation_member-->
    <id column="id" property="id"/>
    <result column="org_id" property="orgId"/>
    <result column="actv_id" property="actvId"/>
    <result column="participation_id" property="participationId"/>
    <result column="user_id" property="userId"/>
    <result column="formal" property="formal"/>
    <result column="join_method" property="joinMethod"/>
    <result column="group_id" property="groupId"/>
    <result column="deleted" property="deleted"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="create_time" property="createTime"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="update_time" property="updateTime"/>
    <result column="db_archived" property="dbArchived"/>
    <result column="join_time" property="joinTime"/>
    <result column="effect_time" property="effectTime"/>
    <result column="start_time" property="startTime"/>
    <result column="end_time" property="endTime"/>
    <result column="delay_flag" property="delayFlag"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org_id, actv_id, participation_id, user_id, formal, join_method, group_id, deleted,
    create_user_id, create_time, update_user_id, update_time,
    db_archived, join_time, effect_time, start_time, end_time, delay_flag
  </sql>
  <select id="findTotalUserCount" resultType="java.lang.Long">
    select count(1)
    from rv_activity_participation_member
    where actv_id = #{actvId} and org_id = #{orgId} and deleted = 0 and participation_id = #{participationId}
  </select>
  <select id="findAllUserIdByActId" resultType="java.lang.String">
    select user_id from rv_activity_participation_member where actv_id = #{actvId} and org_id = #{orgId} and deleted = 0
  </select>

  <select id="listByActvId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/> from rv_activity_participation_member where actv_id = #{actvId} and org_id = #{orgId} and deleted = 0
    and user_id in
    <foreach close=")" collection="userIds" index="index" item="userId" open="(" separator=",">
      #{userId}
    </foreach>
  </select>
  <select id="pageUser" resultMap="BaseResultMap">
    select apm.id
         , apm.actv_id
         , apm.participation_id
         , apm.user_id
         , apm.formal
         , apm.join_method
         , apm.join_time
         , apm.effect_time
         , apm.start_time
         , apm.end_time
         , apm.delay_flag
    from rv_activity_participation_member apm
    join udp_lite_user_sp                 u on u.id = apm.user_id
    where apm.actv_id = #{actvId}
      and u.org_id = #{orgId}
      and apm.org_id = #{orgId}
      and apm.deleted = 0
    <if test="searchParam.deptId != null and searchParam.deptId != ''">
      and u.dept_id = #{searchParam.deptId}
    </if>
    <if test="searchParam.positionId != null and searchParam.positionId != ''">
      and u.position_id = #{searchParam.positionId}
    </if>
    <if test="searchParam.userStatus != null">
      and u.status = #{searchParam.userStatus}
    </if>
    <if test="searchParam.searchKey != null and searchParam.searchKey != ''">
      and (u.username like concat('%', #{searchParam.searchKey}, '%') or
           u.fullname like concat('%', #{searchParam.searchKey}, '%'))
    </if>
  </select>

  <select id="listPage" resultType="com.yxt.talent.rv.application.activity.dto.ActMemberUser">
    select t1.id
         , t1.org_id
         , t1.user_id
         , u.username
         , u.fullname
         , if(u.deleted = 1, 2, u.status) as status
         , u.dept_id
         , u.dept_name
         , u.deleted
         , u.position_id
         , u.position_name
         , t2.result_status
    from rv_activity_participation_member   t1
    left join udp_lite_user                 u on t1.user_id = u.id
    left join rv_base_activity_result t2
              on t1.org_id = t2.org_id and t2.actv_id = #{criteria.actvId} and t1.user_id = t2.user_id and actv_type = 1
    where t1.org_id = #{orgId}
      and t1.deleted = 0
      and t1.actv_id = #{criteria.prjId}
      and t1.participation_id = #{criteria.participationId}
    <if test="criteria.positionIds != null and !criteria.positionIds.empty">
      and u.position_id in
      <foreach close=")" collection="criteria.positionIds" index="index" item="positionId" open="(" separator=",">
        #{positionId}
      </foreach>
    </if>
    <if test="criteria.deptIds != null and !criteria.deptIds.empty">
      and u.dept_id in
      <foreach close=")" collection="criteria.deptIds" index="index" item="deptId" open="(" separator=",">
        #{deptId}
      </foreach>
    </if>

    <if test="criteria.status != null and criteria.status == 0">
      and u.deleted = 0
      and u.status = #{criteria.status}
    </if>
    <if test="criteria.status != null and criteria.status == 1">
      and u.deleted = 0
      and u.status = #{criteria.status}
    </if>
    <if test="criteria.status != null and criteria.status == 2">
      and u.deleted = 1
    </if>

    <if test="criteria.keyword != null and criteria.keyword != ''">
      <if test="criteria.kwType != null and criteria.kwType == 2">
        and u.username like concat('%', #{criteria.keyword}, '%')
      </if>
      <if test="criteria.kwType != null and criteria.kwType == 1">
        and u.fullname like concat('%', #{criteria.keyword}, '%')
      </if>
      <if test="criteria.kwType == null or criteria.kwType == -1">
        and (u.username like concat('%', #{criteria.keyword}, '%') or
             u.fullname like concat('%', #{criteria.keyword}, '%'))
      </if>
    </if>
    ORDER BY t1.`create_time` DESC, t1.`id`
  </select>

  <select id="findUserIdByActIdAndUserIds" resultType="java.lang.String">
    select user_id from rv_activity_participation_member where actv_id = #{actvId} and org_id = #{orgId} and deleted = 0
    and participation_id = #{participationId}
    and user_id in
    <foreach close=")" collection="userIds" index="index" item="userId" open="(" separator=",">
      #{userId}
    </foreach>
  </select>

  <select id="listPrjUserPage" resultType="com.yxt.talent.rv.application.activity.dto.ActMemberUser">
    select t1.id
    , t1.org_id
    , t1.user_id
    , u.username
    , u.fullname
    , if(u.deleted = 1, 2, u.status) as status
    , u.dept_id
    , u.dept_name
    , u.deleted
    , u.position_id
    , u.position_name
    from rv_activity_participation_member   t1
    left join udp_lite_user                 u on t1.user_id = u.id
    where t1.org_id = #{orgId}
    and t1.deleted = 0
    and t1.actv_id = #{criteria.prjId}
    and t1.participation_id = #{criteria.participationId}
    <if test="criteria.positionIds != null and !criteria.positionIds.empty">
      and u.position_id in
      <foreach close=")" collection="criteria.positionIds" index="index" item="positionId" open="(" separator=",">
        #{positionId}
      </foreach>
    </if>
    <if test="criteria.deptIds != null and !criteria.deptIds.empty">
      and u.dept_id in
      <foreach close=")" collection="criteria.deptIds" index="index" item="deptId" open="(" separator=",">
        #{deptId}
      </foreach>
    </if>

    <if test="criteria.status != null and criteria.status == 0">
      and u.deleted = 0
      and u.status = #{criteria.status}
    </if>
    <if test="criteria.status != null and criteria.status == 1">
      and u.deleted = 0
      and u.status = #{criteria.status}
    </if>
    <if test="criteria.status != null and criteria.status == 2">
      and u.deleted = 1
    </if>

    <if test="criteria.allUserIds != null and !criteria.allUserIds.empty">
      and t1.user_id in
      <foreach close=")" collection="criteria.allUserIds" index="index" item="allUserId" open="(" separator=",">
        #{allUserId}
      </foreach>
    </if>
    <choose>
      <when test="criteria.qwUserIds != null and !criteria.qwUserIds.empty">
        and t1.user_id in
        <foreach close=")" collection="criteria.qwUserIds" index="index" item="qwUserId" open="(" separator=",">
          #{qwUserId}
        </foreach>
      </when>
      <otherwise>
        <if test="criteria.keyword != null and criteria.keyword != ''">
          and (u.username like concat('%', #{criteria.keyword}, '%') or
          u.fullname like concat('%', #{criteria.keyword}, '%'))
        </if>
      </otherwise>
    </choose>

    ORDER BY t1.`create_time` DESC, t1.`id`
  </select>
  <select id="countByActvId" resultType="java.lang.Long">
    select count(*)
    from rv_activity_participation_member a
     inner join rv_activity_perf_result b
                on a.org_id = b.org_id
                  and a.user_id = b.user_id
     inner join udp_lite_user_sp c
                on a.org_id = c.org_id
                  and a.user_id = c.id
    where a.org_id = #{orgId}
      and a.actv_id = #{actvId}
      and a.deleted = 0
      and b.actv_perf_id = #{actId}
  </select>
</mapper>