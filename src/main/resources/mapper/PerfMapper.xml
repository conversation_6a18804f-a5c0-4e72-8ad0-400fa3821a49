<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO">
        <!--@mbg.generated-->
        <!--@Table rv_performance-->
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="period_id" property="periodId"/>
        <result column="period_level" property="periodLevel"/>
        <result column="user_id" property="userId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="perf_score" property="perfScore"/>
        <result column="perf_point" property="perfPoint"/>
        <result column="perf_activity" property="perfActivity"/>
        <result column="perf_third_activity_id" property="perfThirdActivityId"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , create_user_id
             , period_id
             , period_level
             , user_id
             , create_time
             , update_user_id
             , update_time
             , perf_score
             , perf_point
             , perf_activity
             , perf_third_activity_id
    </sql>

  <sql id="auth_fragment">
    <!--@ignoreSql-->
    <choose>
      <when test="query.emptyAuth">
        and 1 != 1
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0) and (query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and (u.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        or u.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
        )
      </when>
      <when test="(query.scopeDeptIds != null and query.scopeDeptIds.size() != 0)">
        and u.dept_id in
        <foreach close=")" collection="query.scopeDeptIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
      <when test="(query.scopeUserIds != null and query.scopeUserIds.size() != 0)">
        and u.id in
        <foreach close=")" collection="query.scopeUserIds" index="index" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </when>
    </choose>
  </sql>

    <select id="selectUserPage"
            resultType="com.yxt.talent.rv.controller.manage.perf.viewobj.PerfUserInfoVO">
        select u.id            as userId
             , u.fullname      as fullname
             , u.username      as username
             , u.dept_name     as deptName
             , u.position_name as positionName
        from rv_performance   rp
        join udp_lite_user_sp u on rp.user_id = u.id and u.org_id = #{orgId}
        where u.deleted = 0
          and u.status = 1
          and rp.org_id = #{orgId}
        <choose>
          <when test="deptIds != null and deptIds.size() &gt; 0">
            and u.dept_id in
            <foreach close=")" collection="deptIds" item="deptId" open="(" separator=",">
              #{deptId}
            </foreach>
          </when>
          <otherwise>
            <include refid="auth_fragment"/>
          </otherwise>
        </choose>

        <if test="searchKey != null and searchKey != ''">
          and (u.username like concat('%', #{query.escapedSearchKey}, '%') or u.fullname like concat('%', #{query.escapedSearchKey}, '%'))
          <!--<if test="query.kwType != null and query.kwType == 2">
            and u.username like concat('%', #{query.escapedSearchKey}, '%')
          </if>
          <if test="query.kwType != null and query.kwType == 1">
            and u.fullname like concat('%', #{query.escapedSearchKey}, '%')
          </if>-->
          <!--<if test="query.kwType == null or (query.kwType != 1 and query.kwType != 2)">

          </if>-->
        </if>
        group by rp.user_id, u.create_time
        order by u.create_time desc
    </select>

    <select id="countByToolIdAndUserIds" resultType="java.lang.Integer">
        select count(distinct user_id)
        from rv_performance
        where org_id = #{orgId}
          and period_id = #{toolId}
          and user_id in
        <foreach close=")" collection="existUserIds" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectByOrgIdAndId"
            resultType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where id = #{id}
          and org_id = #{orgId}
    </select>

    <insert id="batchInsertOrUpdate">
        insert into rv_performance
            (id,
             org_id,
             create_user_id,
             period_id,
             period_level,
             user_id,
             create_time,
             update_user_id,
             update_time,
             perf_score,
             perf_point,
             perf_activity,
             perf_third_activity_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.orgId},
             #{item.createUserId},
             #{item.periodId},
             #{item.periodLevel},
             #{item.userId},
             now(),
             #{item.updateUserId},
             now(),
             #{item.perfScore},
             #{item.perfPoint},
             #{item.perfActivity},
             #{item.perfThirdActivityId})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            create_user_id         = values(create_user_id),
            period_id              = values(period_id),
            period_level           = values(period_level),
            user_id                = values(user_id),
            update_user_id         = values(update_user_id),
            update_time            = now(),
            perf_score             = values(perf_score),
            perf_point             = values(perf_point),
            perf_activity          = values(perf_activity),
            perf_third_activity_id = values(perf_third_activity_id)
        </trim>
    </insert>

    <delete id="deleteByOrgIdAndId">
        delete from rv_performance where org_id = #{orgId} and id = #{id}
    </delete>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
    </select>

    <select id="selectByOrgIdAndUseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and user_id = #{userId}
    </select>

    <select id="getPerfByPeriodAndUserIdIfHas" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and period_id = #{periodId}
        <if test="userIds != null and userIds.size() != 0">
            and user_id in
            <foreach close=")" collection="userIds" index="index" item="item" open="("
                     separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectByPeriodIdAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and period_id = #{periodId}
          and user_id = #{userId}
        limit 1
    </select>

    <select id="selectByPeriodIdAndUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and period_id = #{periodId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByPeriodIdsAndUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
        <if test="periodIds != null and periodIds.size() != 0">
            and period_id in
            <foreach close=")" collection="periodIds" index="index" item="item" open="("
                     separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach close=")" collection="userIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectByOrgIdAndPeriodId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and period_id = #{periodId}
    </select>

    <select id="selectPeriodIds" resultType="java.lang.String">
        select distinct period_id from rv_performance where org_id = #{orgId}
    </select>

    <select id="findCountByPeriodId" resultType="java.lang.Integer">
        select count(*) from rv_performance where org_id = #{orgId} and period_id = #{periodId}
    </select>

    <select id="selectPeriodLevel" resultType="java.lang.String">
        select distinct period_level from rv_performance where org_id = #{orgId}
    </select>

    <select id="findPeriodLevel" resultType="java.lang.String">
        select period_level
        from rv_performance
        where org_id = #{orgId}
          and period_level = #{periodLevel}
        limit 1
    </select>

    <delete id="deleteBatch">
        delete from rv_performance
        where org_id = #{orgId}
        <choose>
            <when test="removeIds != null and removeIds.size() != 0">
                and id in
                <foreach close=")" collection="removeIds" index="index" item="item" open="("
                         separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <select id="selectByUserIdAndPerfPeriodId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
          and user_id = #{userId}
          and period_id = #{perfPeriodId}
        order by create_time desc
        limit 1
    </select>

    <select id="selectByUserIdsAndPeriodIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_performance
        where org_id = #{orgId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
        <choose>
            <when test="perfPeriodIds != null and perfPeriodIds.size() != 0">
                and period_id in
                <foreach collection="perfPeriodIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <select id="selectUserIdsByOrgId" resultType="java.lang.String">
        select distinct user_id
        from rv_performance
        where org_id = #{orgId}
    </select>

  <select id="selectDeptIdsByOrgId" resultType="java.lang.String">
    select distinct b.dept_id from rv_performance a
      left join udp_lite_user_sp b
      on a.org_id = b.org_id and a.user_id = b.id
      where a.org_id = #{orgId}
  </select>

  <delete id="deleteAllByOrgId" parameterType="java.lang.String">
    delete from rv_performance
    where org_id = #{orgId}
  </delete>

  <select id="selectUserPerf" resultType="com.yxt.talentrvfacade.bean.UserPerf4Facade">
    select a.user_id, a.period_id, a.period_level, b.grade_name as perfLevelName, c.score_total, a.perf_point
    from rv_performance        a
    join rv_performance_grade  b on a.period_level = b.grade_value and a.org_id = b.org_id
    join rv_performance_period c on c.id = a.period_id and c.org_id = a.org_id
    where a.org_id = #{query.orgId}

  </select>
</mapper>