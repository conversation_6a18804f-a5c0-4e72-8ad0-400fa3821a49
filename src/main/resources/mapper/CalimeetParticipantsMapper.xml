<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO">
        <!--@mbg.generated-->
        <!--@Table rv_calimeet_participants-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="calimeet_id" jdbcType="CHAR" property="calimeetId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="user_type" jdbcType="TINYINT" property="userType"/>
        <result column="cali_status" jdbcType="TINYINT" property="caliStatus"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, calimeet_id, user_id, user_type, cali_status, deleted, create_user_id,
        create_time, update_user_id, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet_participants
        where id = #{id,jdbcType=CHAR}
    </select>
    <select id="selectByCaliMeetId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet_participants
        where org_id =#{orgId} and calimeet_id =#{caliMeetId} and deleted=0
    </select>
    <select id="selectByCaliMeetIds"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet_participants
        where org_id =#{orgId} and deleted=0
        and calimeet_id in
        <foreach collection="caliMeetIds" item="caliMeetId" open="(" close=")" separator=",">
            #{caliMeetId}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from rv_calimeet_participants
        where id = #{id,jdbcType=CHAR}
    </delete>
    <delete id="deleteByMeetId">
        update rv_calimeet_participants set deleted = 1
        where org_id =#{orgId} and calimeet_id =#{caliMeetId} and deleted=0
    </delete>
    <insert id="insert"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO">
        <!--@mbg.generated-->
        insert into rv_calimeet_participants (id, org_id, calimeet_id,
        user_id, user_type, cali_status,
        deleted, create_user_id, create_time,
        update_user_id, update_time)
        values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{calimeetId,jdbcType=CHAR},
        #{userId,jdbcType=CHAR}, #{userType,jdbcType=TINYINT}, #{caliStatus,jdbcType=TINYINT},
        #{deleted,jdbcType=TINYINT}, #{createUserId,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKey"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO">
        <!--@mbg.generated-->
        update rv_calimeet_participants
        set org_id = #{orgId,jdbcType=CHAR},
        calimeet_id = #{calimeetId,jdbcType=CHAR},
        user_id = #{userId,jdbcType=CHAR},
        user_type = #{userType,jdbcType=TINYINT},
        cali_status = #{caliStatus,jdbcType=TINYINT},
        deleted = #{deleted,jdbcType=TINYINT},
        create_user_id = #{createUserId,jdbcType=CHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_user_id = #{updateUserId,jdbcType=CHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=CHAR}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rv_calimeet_participants
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.orgId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="calimeet_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.calimeetId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.userId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.userType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="cali_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.caliStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.deleted,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="create_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="update_user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateUserId,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=CHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=CHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_calimeet_participants
        (id, org_id, calimeet_id, user_id, user_type, cali_status, deleted, create_user_id,
        create_time, update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.calimeetId,jdbcType=CHAR},
            #{item.userId,jdbcType=CHAR}, #{item.userType,jdbcType=TINYINT}, #{item.caliStatus,jdbcType=TINYINT},
            #{item.deleted,jdbcType=TINYINT}, #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <select id="listByCalimeetIdAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet_participants
        where
        org_id =#{orgId} and calimeet_id =#{caliMeetId} and deleted=0
        AND user_id = #{userId,jdbcType=VARCHAR}
    </select>

  <update id="updateByUserId">

  </update>
</mapper>