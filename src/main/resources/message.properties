apis.sptalentrv.sys.error=4444444;451;服务异常。
apis.sptalentrv.sys.operating.frequently=2181016;400;操作频繁。
apis.sptalentrv.sys.concurrent.error=2181404;400;并发错误。
apis.sptalentrv.global.enum.exceed=2181017;400;枚举值不在有效范围。
apis.sptalentrv.global.id.empty=2181013;400;ID不能为空。
apis.sptalentrv.auth.dept_ids.no_permission=2181375;400;无法查看非本人管辖部门的数据
apis.sptalentrv.auth.team.dept_ids.only_one=2181398;400;查看人才看板时,有且只能选中一个部门。
apis.sptalentrv.auth.param.error=2181400;400;参数错误。
apis.sptalentrv.auth.tianhe.request.failed=2181514;400;请求天河失败
apis.sptalentrv.auth.tianhe.token.invalid=2181515;400;天河token无效
apis.sptalentrv.data.auth.prem.not.found=2181516;400;权限不存在
apis.sptalentrv.transmit.import.fileId.invalid=2181405;400;导入的文件ID无效
apis.sptalentrv.transmit.import.file.read.error=2181406;400;导入的文件读取错误
apis.sptalentrv.transmit.import.data.out.limit=2181030;400;导入数据超出限制
apis.sptalentrv.org.id.blank=2181326;400;机构id不能为空
apis.sptalentrv.org.id.notMatch=2181396;400;机构id不匹配
apis.sptalentrv.user.focus.target_id.necessary=2181376;400;关注目标id不能为空
apis.sptalentrv.user.focus.action.range=2181378;400;操作类型范围是[0-1]
apis.sptalentrv.user.focus.target_type.range=2181379;400;关注目标类型范围是[0-1]
apis.sptalentrv.user.pos.infos.not.empty=2181393;400;用户前端搜索参数岗位信息不能为空
apis.sptalentrv.user.not.existed=2181051;400;用户不存在
apis.sptalentrv.user.id.blank=2181274;400;用户id不能为空
apis.sptalentrv.user.third_user_id.necessary=2181395;400;第三方用户id不能为空
apis.sptalentrv.user.career_history_id.necessary=2181437;400;任职履历id不能为空
apis.sptalentrv.user.career_history.occurrence_time.necessary=22010058;400;任职履历发生时间不能为空
apis.sptalentrv.user.reward_punishment_history.rpType.invalid=2181397;400;奖惩类型不合法
apis.sptalentrv.user.reward_punishment_history.rpName.necessary=2181441;400;奖惩名称不能为空
apis.sptalentrv.dept.not.exist=2181399;400;部门不存在
apis.sptalentrv.prj.existed=2181000;400;项目已存在。
apis.sptalentrv.prj.notExists=2181002;400;项目不存在。
apis.sptalentrv.prj.name.notBlank=2181243;400;项目名称不能为空
apis.sptalentrv.prj.category.id.notBlank=2181244;400;项目分类ID不能为空
apis.sptalentrv.prj.id.notBlank=2181246;400;项目ID不能为空
apis.sptalentrv.prj.cloud.is.null=2181253;400;云项目为空
apis.sptalentrv.prj.rule.check.expression=2181005;400;项目规则表达式不合法。
apis.sptalentrv.prj.status=2181023;400;项目正在进行中。
apis.sptalentrv.prj.status.invalid=2181026;400;项目状态不允许。
apis.sptalentrv.prj.manager.not.empty=2181032;400;盘点项目负责人不能为空
apis.sptalentrv.prj.manager.size.exceeded=2181033;400;盘点项目负责人最大支持1000人
apis.sptalentrv.prj.perf.period.name.conflict=2181411;400;绩效周期名称重复
apis.sptalentrv.prj.perf.import.no.username=2181412;400;用户名为空
apis.sptalentrv.prj.perf.import.no.period=2181413;400;绩效周期为空
apis.sptalentrv.prj.perf.import.no.level=2181414;400;绩效为空
apis.sptalentrv.prj.perf.period.not.exist=2181416;404;绩效周期不存在
apis.sptalentrv.prj.perf.period.has.data=2181417;400;周期关联数据
apis.sptalentrv.prj.perf.cycle.necessary=2181435;400;绩效类型不能为空
apis.sptalentrv.prj.perf.period.necessary=2181436;400;绩效周期不能为空
apis.sptalentrv.prj.perf.yearly.necessary=2181442;400;绩效年份不能为空
apis.sptalentrv.prj.perf.sync.period.invalid_1=2181443;400;半年度绩效周期的期数必须等于1或2
apis.sptalentrv.prj.perf.sync.period.invalid_2=2181444;400;季度绩效周期的期数必须等于1-4
apis.sptalentrv.prj.perf.sync.period.invalid_3=2181445;400;月度绩效周期的期数必须等于1-12
apis.sptalentrv.prj.perf.sync.period.invalid_4=2181446;400;年度绩效周期的期数必须等于年份
apis.sptalentrv.prj.perf.sync.error_1=2181447;400;绩效总分和绩效等级必须有一个有值
apis.sptalentrv.prj.perf.sync.error_2=2181448;400;绩效得分时，必须要有绩效总分
apis.sptalentrv.prj.perf.grade.name.conflict=2181425;400;绩效等级值重复
apis.sptalentrv.prj.perf.grade.not.exist=2181426;400;绩效等级不存在
apis.sptalentrv.prj.dim.max.exceed=2181418;400;自定义维度上限为20
apis.sptalentrv.prj.dim.name.conflict=2181419;400;维度名称重复
apis.sptalentrv.prj.dim.not.exist=2181420;404;维度不存在
apis.sptalentrv.prj.dim.enabled=2181421;400;维度已经是启用状态
apis.sptalentrv.prj.dim.disabled=2181422;400;维度已禁用
apis.sptalentrv.prj.dim.config.count=2181004;400;项目维度配置计数小于 2。
apis.sptalentrv.prj.dim.not.configured=2181007;400个已存在维度未配置。
apis.sptalentrv.prj.dim.config.not.existed=2181009;400;维度配置不存在
apis.sptalentrv.prj.dim.conf.tool.notExists=2181031;404;维度工具不存在
apis.sptalentrv.prj.dim.tool.not.configured=2181012;400;未配置项目维度工具。
apis.sptalentrv.prj.dim.enable=2181019;400;维度已启用。
apis.sptalentrv.prj.dim.deleted=2181020;400;维度已删除。
apis.sptalentrv.prj.dim.tool.exist.same.model=2181022;400;尺寸工具存在相同型号。
apis.sptalentrv.prj.dim.tools.missing=2181035;400;项目维度工具缺失
apis.sptalentrv.prj.dim.conf.not.exists=2181040;400;项目维度配置不存在
apis.sptalentrv.prj.dim.not.config=2181041;400;项目未配置维度
apis.sptalentrv.prj.dim.conf.invalid=2181053;400;维度配置不完整
apis.sptalentrv.prj.dim.system.notDisabled=2181230;400;维度系统未禁用
apis.sptalentrv.prj.dim.name.size.out.limit=2181235;400;维度名称长度超出限制
apis.sptalentrv.prj.dim.remark.size.out.limit=2181236;400;维度备注长度超出限制
apis.sptalentrv.prj.dim.conf.notBlank=2181245;400;维度配置ID不能为空
apis.sptalentrv.prj.dim.been.used=2181423;400;维度已被使用
apis.sptalentrv.prj.dim.rule.not.existed=2181428;400;维度规则不存在
apis.sptalentrv.prj.dim.not.existed=2181429;400;维度不存在
apis.sptalentrv.prj.user.add.must.not.null=2181042;400;项目添加用户不能为空
apis.sptalentrv.prj.user.remove.not.null=2181043;400;项目移除用户不能为空
apis.sptalentrv.prj.user.must.not.null=2181044;400;项目用户不能为空
apis.sptalentrv.prj.user.result.calc.type.import.overflow=2181047;400;项目计算类型导入溢出
apis.sptalentrv.prj.user.result.calc.update.import.overflow=2181048;400;项目计算更新导入溢出
apis.sptalentrv.prj.user.result.import.fileId.invalid=2181252;400;计算结果导入的文件ID无效
apis.sptalentrv.prj.user.suggestion.size=2181238;400;用户建议长度超出限制
apis.sptalentrv.prj.eval.not.existed=2181010;400;测评不存在或已删除。
apis.sptalentrv.prj.eval.user.check.error=2181039;400;检查评估用户错误
apis.sptalentrv.prj.eval.relation.necessary=2181024:400;该项目维度测评存在人员未设置评估关系，请先设置
apis.sptalentrv.prj.eval.overflow=2181046;400;项目评估费用溢出
apis.sptalentrv.prj.eval.id.null=2181394;400;评估id不能为空
apis.sptalentrv.prj.label.name.conflict=2181052;400;项目标签名称冲突
apis.sptalentrv.prj.grade.name.not.null=2181239;400;等级名称不能为空
apis.sptalentrv.prj.grade.name.size.out.limit=2181240;400;等级名称长度超出限制
apis.sptalentrv.prj.period.name.not.null=2181241;400;周期名称不能为空
apis.sptalentrv.prj.period.name.size.out.limit=2181242;400;周期名称长度超出限制
apis.sptalentrv.prj.training.user_id.empty=2181382;400;绑定培训项目时用户id不能为空
apis.sptalentrv.prj.training.training_id.empty=2181427;400;绑定培训项目时培训id不能为空
apis.sptalentrv.prj.mine.status.range=2181429;400;项目状态范围错误
apis.sptalentrv.prj.label.rule.not.empty=2181501;400;项目标签规则不能为空
apis.sptalentrv.prj.logic.not.null=2181502;400;项目逻辑不能为空
apis.sptalentrv.prj.logic.range.over=2181503;400;项目逻辑范围错误
apis.sptalentrv.prj.label.rule.target.id.not.null=2181504;400;项目标签规则目标id不能为空
apis.sptalentrv.prj.label.rule.operators.not.empty=2181505;400;项目标签规则操作符不能为空
apis.sptalentrv.prj.label.rule.operator.not.blank=2181506;400;项目标签规则操作符不能为空
apis.sptalentrv.prj.label.rule.value.not.null=2181507;400;项目标签规则值不能为空
apis.sptalentrv.prj.result.label.conflict=2181438;400;人才定义名称重复
apis.sptalentrv.prj.result.notExists=2181439;400;人才定义不存在
apis.sptalentrv.prj.dimension.not.config=2181512;400;项目中有维度未配置
apis.sptalentrv.perf.cycle.range=2181432;400;绩效类型不在范围内
apis.sptalentrv.perf.yearly.range=2181433;400;绩效年份不在范围内
apis.sptalentrv.perf.used=2181434;400;绩效周期被使用不允许删除
apis.sptalentrv.perf.level.used=2181435;400;绩效等级被使用不允许删除
apis.sptalentrv.perf.key.exist=2181436;400;当前绩效年份下已经存在相同的绩效类型和绩效周期
apis.sptalentrv.perf.level.state.used=2181437;400;绩效等级启用状态下不允许删除
apis.sptalentrv.perf.period.empty=2181440;400;绩效周期不能为空
apis.sptalentrv.perf.score.total.range=2181449;400;绩效总分范围错误
apis.sptalentrv.perf.clear.active.projects=2181450;400;存在使用绩效数据的未结束盘点项目，无法清空绩效数据
apis.sptalentrv.perf.user.ids.empty=2181451;400;绩效用户id不能为空
apis.sptalentrv.perf.period.ids.empty=2181452;400;绩效周期id不能为空
apis.sptalentrv.perf.user.ids.too.many=2181453;400;绩效用户id数量超过1000

apis.sptalentrv.category.existed=2181001;400;类别已存在。
apis.sptalentrv.category.not.existed=2181003;400;类别不存在。
apis.sptalentrv.category.used=2181050;400;分类已被使用
apis.sptalentrv.category.validation.name.NotBlank=2181054;400;分类名称不能为空
apis.sptalentrv.category.max.count=2181229;400;分类数量达到上限
apis.sptalentrv.category.name.size.exceed=2181234;400;分类名称长度超出限制
apis.sptalentrv.calimeet.not.existed=2181006;400;calMeet 不存在。
apis.sptalentrv.calimeet.user.not.existed=2181008;400;会议用户不存在。
apis.sptalentrv.calimeet.name.existed=2181013;400;校准会议名称已存在。
apis.sptalentrv.calimeet.user.batch_add.list.empty=2181014;400;用户列表为空。
apis.sptalentrv.calimeet.user.not.exists=2181015;400;用户不在项目中。
apis.sptalentrv.calimeet.delete.forbidden=2181018;400;无法删除。
apis.sptalentrv.calimeet.name.notBlank=2181231;400;校准会议名称不能为空
apis.sptalentrv.calimeet.name.size.exceed=2181232;400;校准会议名称长度超出限制
apis.sptalentrv.calimeet.minutes.exceed=2181233;400;校准会议时长超出限制
apis.sptalentrv.calimeet.user.userIdList.notNull=2181237;400;会议用户ID列表不能为空
apis.sptalentrv.calimeet.user.suggestion.size=2181238;400;会议用户建议长度超出限制
apis.sptalentrv.calimeet.meetTime.notBlank=2181513;400;会议时间不能为空
apis.sptalentrv.calimeet.score.over.maxs=2181511;400;校准分数不能大于等于10000
apis.sptalentrv.dmp.task.not_start=2181300;400;无法发布，方案设计中任务未全部启用，请前往操作
apis.sptalentrv.dmp.rule.notExists=2181301;400;项目中匹配规则未设置
apis.sptalentrv.dmp.dmpMatchRule.dmp.hadSet=2181302;400;匹配规则已存在
apis.sptalentrv.dmp.dmpMatchRule.dmp.notMatch=2181303;400;匹配规则和当前项目不匹配
apis.sptalentrv.dmp.matchRule.type.notNull=2181305;400;匹配规则类型不为空
apis.sptalentrv.dmp.matchRule.notExist=2181306;400;匹配规则不存在
apis.sptalentrv.dmp.dmpUser.ids.notEmpty=2181307;400;用户不为空
apis.sptalentrv.dmp.matchRule.layerRule.layerType.notNull=2181308;400;匹配规则的分层设置类型为空
apis.sptalentrv.dmp.userId.dmpId.notMatch=2181309;400;用户和项目信息不匹配
apis.sptalentrv.dmp.matchRule.type.overRange=2181311;400;匹配规则类型错误
apis.sptalentrv.dmp.matchRule.layerRule.layerName.notBlank=2181312;400;匹配规则的分层名称为空
apis.sptalentrv.dmp.matchRule.layerRule.leastOne=2181313;400;匹配规则的分层设置不为空
apis.sptalentrv.dmp.dmpMsgConfig.sendFlag.overRange=2181314;400;消息配置发送标识不存在
apis.sptalentrv.dmp.matchRule.layerRule.layerValue.notNull=2181315;400;匹配规则分层设置值不为空
apis.sptalentrv.dmp.eval.getDimScoreByProjectId.error=2181316;400;调用测评获取维度信息失败
apis.sptalentrv.dmp.dmpUser.ids.position.notMatch=2181317;400;添加人员和项目岗不匹配
apis.sptalentrv.dmp.name.repeat=2181318;400;项目名称重复
apis.sptalentrv.dmp.copy.status.error=2181319;400;已经完成的项目才能被复制
apis.sptalentrv.dmp.notExists=2181320;400;人岗匹配项目不存在
apis.sptalentrv.dmp.finished.cannot.modify=2181321;400;已经完成的项目不能修改
apis.sptalentrv.dmp.hadFinished=2181323;400;项目已结束不支持修改信息
apis.sptalentrv.dmp.user.checkin.max.size=2181324;400;待检的人员数量超过1000人上限
apis.sptalentrv.dmp.task_type.not.null=2181325;400;任务类型不能为空
apis.sptalentrv.dmp.id.not.blank=2181327;400;dmpId不能为空
apis.sptalentrv.dmp.task_name.not.blank=2181328;400;dmpId不能为空
apis.sptalentrv.dmp.task_name.conflict=2181329;400;任务名称已存在
apis.sptalentrv.dmp.not.existed=2181330;400;dmp不存在
apis.sptalentrv.dmp.task.not.existed=2181331;400;动态岗位匹配任务不存在
apis.sptalentrv.dmp.task_id.not.blank=2181332;400;动态岗位匹配任务id不能为空
apis.sptalentrv.dmp.jq_dim.not.existed=2181333;400;动态岗位匹配任务维度不存在
apis.sptalentrv.dmp.form.config.empty=2181339;400;动态岗位匹配任务表单配置不能为空
apis.sptalentrv.dmp.task.active_status.not.null=2181341;400;动态岗位匹配任务激活状态不能为空
apis.sptalentrv.dmp.task.active_status.range.exceeded=2181342;400;动态岗位匹配任务激活状态范围超出
apis.sptalentrv.dmp.match.rule.not.found=2181344;400;动态岗位匹配规则未找到
apis.sptalentrv.dmp.rule.layer.not.found=2181347;400;动态岗位匹配规则分层未找到
apis.sptalentrv.dmp.plan.used=2181349;400;该岗位方案已经被使用
apis.sptalentrv.dmp.plan.id.not.blank=2181350;400;人岗匹配项目的任职资格方案id不能为空
apis.sptalentrv.dmp.task.empty=2181351;400;方案设计中未配置任务，请前往配置
apis.sptalentrv.dmp.dmpMatchRule.layerName.notRepeat=2181353;400;匹配规则分层名称不可重复
apis.sptalentrv.dmp.auth.error=2181354;400;当前操作人无操作权限
apis.sptalentrv.dmp.disableEval.range=2181355;400;动态项目禁用学员参与表单测评的开关字段范围超出
apis.sptalentrv.dmp.disableEval.error=2181356;400;动态项目禁用学员参与表单测评的开关字段错误
apis.sptalentrv.dmp.stopTime.error=2181357;400;动态项目自动结束时间不能小于项目指定的结束时间
apis.sptalentrv.dmp.conf.not.exists=2181358;400;动态项目配置不存在
apis.sptalentrv.dmp.endTime.expired=2181359;400;动态项目截止时间已过期
apis.sptalentrv.dmp.cannot.publish.or.timed=2181360;400;动态项目不能发布或定时发布
apis.sptalentrv.dmp.launch_time.expired=2181361;400;动态项目定时发布时间必须在当前时间之后
apis.sptalentrv.dmp.id.empty=2181362;400;动态项目id不能为空
apis.sptalentrv.dmp.status.notOperate=2181364;400;动态项目状态不可操作
apis.sptalentrv.dmp.dmpName.size=2181365;400;动态项目名称长度超出限制
apis.sptalentrv.dmp.dmpUser.positionId.notMatch=2181367;400;用户和项目岗不匹配
apis.sptalentrv.dmp.dmpUser.import.empty=2181369;400;导入的用户列表为空
apis.sptalentrv.dmp.dmpUser.import.fail=2181370;400;导入的用户列表失败
apis.sptalentrv.dmp.status.notTimedPublish=2181372;400;动态项目状态不是定时发布
apis.sptalentrv.dmp.task.create.failed=2181373;400;动态项目任务创建失败
apis.sptalentrv.dmp.plan.empty=2181374;400;岗位方案保存不能为空
apis.sptalentrv.dmp.copy.dimId.null=2181384;400;复制的维度id不能为空
apis.sptalentrv.dmp.copy.rule.null=2181385;400;复制的规则不能为空
apis.sptalentrv.dmp.rule_weight.error=2181386;400;规则权重错误
apis.sptalentrv.dmp.copy.rule.layer.null=2181387;400;复制的规则分层不能为空
apis.sptalentrv.dmp.copy.task.null=2181389;400;复制的任务不能为空
apis.sptalentrv.dmp.rule.score.detail.empty=2181390;400;规则分数详情不能为空
apis.sptalentrv.dmp.task.job.not.exists=2181409;400;动态岗位匹配任务job不存在
apis.sptalentrv.dmp.user.not.exists=2181410;400;动态岗位匹配任务用户不存在
apis.sptalentrv.dmp.task.dim.type.not.null=2181419;400;动态岗位匹配任务维度类型不能为空
apis.sptalentrv.dmp.task.dim.jq_dim.id.not.empty=2181420;400;动态岗位匹配任务维度id不能为空
apis.sptalentrv.dmp.task.dim.empty=2181421;400;动态岗位匹配任务维度不能为空
apis.sptalentrv.dmp.project.dim.id.is.null=2181422;400;项目维度id为空
apis.sptalentrv.dmp.copy.task.dim.null=2181423;400;复制的任务维度不能为空
apis.sptalentrv.dmp.rule.not.existed=2181424;400;规则不存在
apis.sptalentrv.dmp.task.desc.too.long=2181428;400;任务说明超出长度限制
apis.sptalentrv.dmp.remark.size=2181429;400;备注长度超出限制
apis.sptalentrv.dmp.user.auto.group.not.exists=2181431;400;自动分组不存在
apis.sptalentrv.dmp.startEndTime.invalid=2181508;400;开始时间不能大于结束时间
apis.sptalentrv.perf.level.no.right=2181510;400;绩效等级不存在或已禁用
apis.sptalentrv.ai.helper.chat.pair.currNodeInstanceId.not.null=2181517;400;当前节点实例id不能为空
apis.sptalentrv.ai.helper.chat.pair.modelUserContent.not.null=2181518;400;用户的提问不能为空
apis.sptalentrv.ai.helper.chat.sessionId.not.blank=2181519;400;会话id不能为空
apis.sptalentrv.ai.helper.chat.message.useful.exceed=2181520;400;有用标识超出限制，0-无用 1-有用
apis.sptalentrv.ai.helper.chat.answer.not.null=2181521;400;回答不能为空
apis.sptalentrv.ai.helper.chat.question.not.null=2181522;400;问题不能为空
apis.sptalentrv.ai.helper.chat.session.not.found=2181523;400;会话不存在
# xpd项目:2182000~2182050
apis.sptalentrv.xpd.status.can.not.edit=2182000;400;当前状态不能编辑
apis.sptalentrv.xpd.status.can.not.publish=2182001;400;当前状态不能发布
apis.sptalentrv.xpd.status.can.not.delete=2182002;400;当前状态不能删除
apis.sptalentrv.xpd.status.can.not.finish=2182003;400;当前状态不能结束
apis.sptalentrv.xpd.prj.not.exist=2182004;400;盘点项目不存在
apis.sptalentrv.xpd.model.notExist=2182005;400;模型不存在
apis.sptalentrv.xpd.model.dim.notExist=2182006;400;模型中不存在维度
apis.sptalentrv.xpd.model.dim.size=2182007;400;人才模型可盘点维度不足2个，请重新选择
apis.sptalentrv.xpd.project.can.not.finish=2182008;400;项目不能结束
apis.sptalentrv.xpd.project.name.not.blank=2182009;400;请输入项目名称
apis.sptalentrv.xpd.project.name.size=2182010;400;最多支持200个字
apis.sptalentrv.xpd.project.categoryId.not.blank=2182011;400;请选择项目分类
apis.sptalentrv.xpd.project.manager.not.empty=2182012;400;请输入项目名称
apis.sptalentrv.xpd.project.description.size=2182013;400;最多支持2000个字
apis.sptalentrv.xpd.project.startTime.not.null=2182014;400;请填写起止日期
apis.sptalentrv.xpd.project.endTime.not.null=2182015;400;请填写起止日期
apis.sptalentrv.xpd.project.modelId.not.empty=2182016;400;请选择盘点模型
apis.sptalentrv.xpd.project.manager.max=2182017;400;负责人最多支持30个
apis.sptalentrv.xpd.rule.conf.check.fail=2182018;400;盘点规则存在异常，请调整并保证配置正确！
apis.sptalentrv.xpd.part.not.exist=2182019;400;盘点项目参与者未找到！
apis.sptalentrv.actv.notexist=2182020;400;活动不存在！
apis.sptalentrv.audit.error=2182021;400;请配置审核流程
apis.sptalentrv.date.range.error=2182022;400;盘点项目时间设置结束时间必须大于起始时间

# xpd落位规则:2182050~2182100
apis.sptalentrv.xpd.rule.conf.not.found=2182050;400;落位规则配置未找到
# xpd落位结果:2182100~2182150
apis.sptalentrv.xpd.result.query.targetId.not.blank=2182100;400;维度id不能为空
apis.sptalentrv.xpd.result.query.dimCombId.not.blank=2182101;400;维度组合id不能为空
apis.sptalentrv.xpd.result.query.queryType.invalid=2182102;400;查询类型无效
# xpd计算:2182150~2182200
# xpd结果导入:2182200~2182250
# xpd盘点设置:2182250~2182400
apis.sptalentrv.xpd.grid.not.found=2182250;400;盘点宫格未找到
apis.sptalentrv.xpd.grid.cell.not.found=2182251;400;盘点宫格单元格未找到
apis.sptalentrv.xpd.grid.ratio.not.found=2182252;400;盘点宫格比例未找到
apis.sptalentrv.xpd.level.name.conflict=2182253;400;人才分级名称重复
apis.sptalentrv.xpd.level.null=2182254;400;人才分级不存在
apis.sptalentrv.xpd.level.xpdId.notnull=2182255;400;人才分级项目ID不能为空
apis.sptalentrv.xpd.level.xpdRuleId.notnull=2182256;400;人才分级项目规则ID不能为空
apis.sptalentrv.xpd.level.gridId.notnull=2182257;400;人才分级宫格ID不能为空
apis.sptalentrv.xpd.level.levelName.notnull=2182258;400;人才层级名称不能为空
apis.sptalentrv.xpd.level.levelName.maxlength=2182259;400;人才层级名称最多支持20个字
apis.sptalentrv.xpd.level.levelNameI18n.notnull=2182260;400;人才层级名称国际化code不能为空
apis.sptalentrv.xpd.level.levelValue.maxlength=2182261;400;人才层级分层值:比例或固定值上限999
apis.sptalentrv.xpd.level.competent.error=2182262;400;人才层级是否胜任设置错误，0-不胜任 1-胜任
apis.sptalentrv.xpd.level.icon.maxlength=2182263;400;人才层级层级icon地址最多支持500个字
apis.sptalentrv.xpd.level.formula.notnull=2182264;400;人才层级计算规则表达式不能为空
apis.sptalentrv.xpd.level.formulaDisplay.notnull=2182265;400;人才层级可视化的计算规则表达式不能为空
apis.sptalentrv.xpd.level.orderIndex.notnull=2182266;400;人才分级排序不能为空
apis.sptalentrv.xpd.level.orderIndex.error=2182267;400;人才分级排序最大999
apis.sptalentrv.xpd.grid.type.invalid=2182268;400;盘点宫格类型无效
apis.sptalentrv.xpd.dimComb.not.found=2182269;400;维度组合未找到
apis.sptalentrv.xpd.dimComb.xpdId.invalid=2182270;400;盘点项目ID无效
apis.sptalentrv.xpd.dim.comb.null=2182271;400;维度组合不存在
apis.sptalentrv.xpd.dim.comb.name.conflict=2182272;400;维度组合名称重复
apis.sptalentrv.xpd.dim.comb.xSdDimId.notnull=2182273;400;维度组合x轴维度id不能为空
apis.sptalentrv.xpd.dim.comb.ySdDimId.notnull=2182274;400;维度组合y轴维度id不能为空
apis.sptalentrv.xpd.dim.comb.dimId.error=2182275;400;维度组合x轴维度和y轴维度不能一样
apis.sptalentrv.xpd.dim.comb.combName.notnull=2182276;400;维度组合名称不能为空
apis.sptalentrv.xpd.dim.comb.combName.maxlength=2182277;400;维度组合名称最多支持200个字
apis.sptalentrv.xpd.dim.comb.combDesc.notnull=2182278;400;维度组合描述不能为空
apis.sptalentrv.xpd.dim.comb.combDesc.maxlength=2182279;400;维度组合描述最多支持2000个字
apis.sptalentrv.xpd.dim.comb.notdeleted=2182280;400;内置维度组合不可删除
apis.sptalentrv.xpd.dim.comb.inuse.notchange=2182280;400;在使用中维度组合的不可编辑或删除
apis.sptalentrv.xpd.dimComb.id.empty=2182281;400;维度组合id不能为空
apis.sptalentrv.xpd.grid.id.empty=2182282;400;盘点宫格id不能为空
apis.sptalentrv.xpd.cell.index.empty=2182283;400;盘点宫格单元格序号不能为空
# xpd盘点人员：2182400~2182500
apis.sptalentrv.xpd.user.result.export.aom.actId.not.exist=2182400;400;盘点结果绩效维度没有绑定任何绩效活动
apis.sptalentrv.xpd.user.result.export.activity.arrange.not.exist=2182401;400;盘点结果绩效维度没有绑定任何绩效活动
apis.sptalentrv.xpd.user.result.export.activity.arrange.ext.not.exist=2182402;400;盘点结果绩效维度没有绑定任何绩效活动
apis.sptalentrv.xpd.user.result.export.periodids.not.exist=2182403;400;盘点结果绩效维度没有绑定任何绩效活动
apis.sptalentrv.xpd.user.memberId.null=2182404;400;盘点的项目人员id不能为空
# xpd其他：2182500~2182600
apis.sptalentrv.xpd.actionplan.command.targetType.invalid=2182500;400;目标类型无效
apis.sptalentrv.xpd.actionplan.command.trainingId.empty=2182500;400;请选择培训项目
apis.sptalentrv.xpd.actionplan.command.poolId.empty=2182500;400;请选择人才池
apis.sptalentrv.xpd.actionplan.command.dim.invalid=2182501;400;请选择盘点维度
apis.sptalentrv.xpd.actionplan.command.dimcomb.invalid=2182501;400;请选择维度组合
apis.sptalentrv.xpd.actionplan.command.gridlevelId.invalid=2182501;400;请选择维度分层
apis.sptalentrv.xpd.actionplan.command.cellId.invalid=2182501;400;请选择宫格
apis.sptalentrv.xpd.actionplan.command.levelId.invalid=2182501;400;请选择项目分层
apis.sptalentrv.xpd.actionplan.null=2182509;400;行动计划不存在
apis.sptalentrv.xpd.action.plan.command.radio.type.invalid=2182510;400;行动计划类型无效
apis.sptalentrv.xpd.actionplan.userId.empty=2182511;400;根据所选条件未查询到盘点人员，无法加入培训
# activity绩效评估活动: 2183000~2183050
apis.sptalentrv.perf.activity.null=2183000;400;绩效活动不存在
apis.sptalentrv.perf.activity.id.notnull=2183001;400;绩效活动ID未传
apis.sptalentrv.perf.activity.periods.notnull=2183002;400;请选择绩效周期
apis.sptalentrv.perf.activity.indicator.notnull=2183003;400;请选择关联指标
apis.sptalentrv.perf.activity.evaltype.notnull=2183004;400;请选择评估方式
apis.sptalentrv.perf.activity.evaltype.valueerror=2183005;400;评估方式设置的值范围错误，1-绩效等级, 2-绩效得分
apis.sptalentrv.perf.activity.evaltimetype.notnull=2183006;400;请选择评估时间的方式
apis.sptalentrv.perf.activity.evaltimetype.valueerror=2183007;400;评估时间的方式设置值错误，1:动态评估,2:定时评估
apis.sptalentrv.perf.activity.evaltime.notnull=2183008;400;定时评估必须设置定时时间
apis.sptalentrv.perf.activity.name.notnull=2183009;400;请输入活动名称
apis.sptalentrv.perf.activity.name.maxlength=2183010;400;活动名称最多支持200个字
apis.sptalentrv.perf.activity.desc.maxlength=2183011;400;任务名称最多支持500个字
apis.sptalentrv.perf.activity.scorequalified.notnull=2183012;400;请输入达标得分
apis.sptalentrv.perf.conf.periodid.notnull=2183013;400;绩效ID不能为空
apis.sptalentrv.perf.conf.weight.notnull=2183014;400;权重不能为空
apis.sptalentrv.perf.activity.conflist.notnull=2183015;400;请配置计算逻辑
apis.sptalentrv.perf.activity.resultconf.notnull=2183015;400;请配置等级
apis.sptalentrv.perf.activity.weight.error=2183016;400;绩效周期权重之和必须等于100%
apis.sptalentrv.perf.conf.actvperfid.notnull=2183017;400;绩效活动ID不能为空
apis.sptalentrv.perf.conf.resultname.notnull=2183018;400;请输入等级名称
apis.sptalentrv.perf.conf.resultname.maxlength=2183019;400;等级名称最多支持200个字
apis.sptalentrv.perf.conf.qualified.notnull=2183020;400;请选择是否达标
apis.sptalentrv.perf.conf.qualified.error=2183021;400;是否达标设置值错误，0-不达标, 1-达标
apis.sptalentrv.perf.activity.resultconflist.notnull=2183022;400;请配置评估结果
apis.sptalentrv.perf.conf.score.maxlength=2183023;400;分数最大999
apis.sptalentrv.perf.conf.rulescore.notnull=2183024;400;绩效评估选择绩效得分时，需要设置分数
apis.sptalentrv.perf.conf.ruleconf.notnull=2183025;400;绩效评估选择绩效等级时，需要设置评估规则
apis.sptalentrv.perf.activity.aomactid.notnull=2183026;400;活动ID不能为空
apis.sptalentrv.perf.activity.period.empty=2183026;400;绩效周期未找到
apis.sptalentrv.perf.activity.period.importerror=2183026;400;导入模板错误
apis.sptalentrv.perf.conf.ordernum.notnull=2183026;400;排序号不能为空
apis.sptalentrv.perf.activity.model.notnull=2183026;400;模型ID不能为空
apis.sptalentrv.perf.activity.indicator.exists=2183027;400;该绩效指标已被设置
apis.sptalentrv.perf.activity.actvid.error=2183027;400;绩效评估ID不能为空
apis.sptalentrv.aom.activity.null=2183027;400;活动不存在
apis.sptalentrv.xpd.status.can.not.edit.for.doing=2183027;400;进行中的活动，仅支持编辑项目名称、分类、负责人、起止时间、盘点目标
apis.sptalentrv.perf.activity.periodid.notexist=2183028;400;绩效周期不存在
apis.sptalentrv.perf.activity.periodid.totalscore.notexist=2183029;400;存在总分不存在的绩效周期
apis.sptalentrv.perf.activity.model.perf.dim.notExist=2183030;400;该模型不存在绩效指标
apis.sptalentrv.perf.activity.form.data.error=2183031;400;绩效活动表单数据提交错误
# activity动态人才评估: 2183050~2183100
# 宫格设置 2183101~2183150
apis.sptalentrv.xpd.grid.comb.empty=2183101;400;维度组合为空
apis.sptalentrv.xpd.grid.not.exist=2183102;400;宫格设置不存在
apis.sptalentrv.xpd.grid.publish=2183103;400;已经发布的不允许编辑
apis.sptalentrv.xpd.grid.cell.empty=2183104;400;宫格组合不能为空
apis.sptalentrv.xpd.grid.cell.not.used=2183105;400;所有宫格都要配置落位比例
apis.sptalentrv.xpd.import.dim.exist=2183106;400;此维度已有导入记录，请勿重复创建
apis.sptalentrv.xpd.import.sheetName.error=2183107;400;导入模板错误
apis.sptalentrv.xpd.import.act.empty=2183108;400;导入内容为空
apis.sptalentrv.xpd.calc.result.ing=2183107;400;结果计算中
apis.sptalentrv.profile.id.notEmpty=2184101;400;动态人才评估活动ID为空
apis.sptalentrv.profile.notFound=2184102;400;动态人才评估活动不存在
apis.sptalentrv.progile.activity.param.error=2184103;400;动态人才评估活动参数错误
apis.sptalentrv.xpd.notExist=2184104;400;盘点项目不存在
apis.sptalentrv.profile.indicator.notFound=2184105;400;动态人才评估活动未关联指标
apis.sptalentrv.profile.interface.calculate.lock=2184106;400;动态人才评估活动正在计算中，请勿重复请求
apis.sptalentrv.tree.node.exist.xpd.proj=2184107;400;该分类下有盘点项目，不能删除
apis.sptalentrv.xpd.grid.name.repeat=2184108;400;宫格模板名称重复
apis.sptalentrv.xpd.grid.show.type.one=2184109;400;只能选择一个默认展示的维度组合。
apis.sptalentrv.xpd.grid.dim.comb.repeat=2184110;400;维度组合不能重复
apis.sptalentrv.xpd.grid.temp.in.delete=2184111;400;内置模板不能删除
apis.sptalentrv.xpd.grid.temp.publish=2184112;400;宫格模板已经发布的不能删除
apis.sptalentrv.xpd.import.ind.score.error=2184113;400;指标分数为小于999的正整数
apis.sptalentrv.xpd.grid.show.type.zero=2184109;400;请选择一个默认展示的维度组合。

# 盘点规则
apis.sptalentrv.xpd.rule.notexist=22010001;400;项目规则不存在
apis.sptalentrv.xpd.rule.conf.id.empty=22010002;400;全局规则ID不能为空
apis.sptalentrv.xpd.rule.conf.notexist=22010003;400;全局规则不存在
apis.sptalentrv.xpd.rule.conf.exist=22010004;400;全局规则已存在，不可二次创建
apis.sptalentrv.xpd.rule.conf.modified=22010005;400;全局规则已被修改，请刷新页面重新再试
apis.sptalentrv.xpd.rule.conf.generating=22010006;400;他人正在生成维度规则，请稍等
apis.sptalentrv.xpd.rule.conf.version.empty=22010007;400;全局规则版本不可为空
apis.sptalentrv.xpd.rule.conf.dim.empty=22010008;400;盘点维度不可为空
apis.sptalentrv.xpd.rule.conf.dimids.empty=22010009;400;盘点维度不可为空
apis.sptalentrv.xpd.rule.conf.resulttype.empty=22010010;400;结果类型不可为空
apis.sptalentrv.xpd.rule.conf.gridid.empty=22010011;400;宫格模板不可为空
apis.sptalentrv.xpd.rule.calc.empty=22010012;400;暂未配置计算规则
apis.sptalentrv.xpd.rule.calc.dim.import.error=22010013;400;计算维度中存在仅导入结果的维度，请调整
apis.sptalentrv.xpd.rule.calc.indicator.empty=22010053;400;指标为空，请检查
apis.sptalentrv.xpd.rule.calc.indicator.notexist=22010014;400;指标不存在，请检查
apis.sptalentrv.xpd.rule.calc.indicator.ref.empty=22010015;400;指标数据来源为空，请检查
apis.sptalentrv.xpd.rule.calc.indicator.ref.error=22010016;400;指标数据来源错误，请检查
apis.sptalentrv.xpd.rule.calc.indicator.calcmethod.error=22010017;400;计算逻辑不正确，请检查
apis.sptalentrv.xpd.rule.level.empty=22010018;400;分层规则未配置
apis.sptalentrv.xpd.rule.level.error=22010019;400;分层规则有误
apis.sptalentrv.xpd.rule.levelvalue.outofrange=22010020;400;分层规则分数超过总分，请检查
apis.sptalentrv.xpd.rule.level.percent.sum.error=22010021;400;人员之和必须为100%
apis.sptalentrv.xpd.rule.level.value.decrease.error=22010022;400;分层规则的值必须递减
apis.sptalentrv.xpd.rule.level.value.min.zero.error=22010023;400;最低分层分值必须为0
apis.sptalentrv.xpd.rule.resulttype.error1=22010024;400;存在导入维度结果的盘点维度，结果类型只能选择维度分层结果
apis.sptalentrv.xpd.rule.resulttype.error2=22010024;400;结果类型不正确，只能选择全局规则中配置的结果类型
apis.sptalentrv.xpd.rule.resulttype.error3=22010024;400;结果类型不正确，只能选择维度分层结果和全局规则中配置的结果类型
apis.sptalentrv.xpd.rule.calc.dim.duplicate=22010025;400;计算规则中不能重复选择维度
apis.sptalentrv.xpd.rule.indicator.duplicate=22010026;400;计算规则中不能重复选择指标
apis.sptalentrv.xpd.rule.calc.dim.notexist=22010027;400;计算规则中的维度不存在
apis.sptalentrv.xpd.rule.calc.weight.empty=22010028;400;权重不能为空
apis.sptalentrv.xpd.rule.calc.weight.sum.error=22010029;400;权重之和必须为100%
apis.sptalentrv.xpd.rule.calc.formular.error=22010030;400;高级公式配置有误
apis.sptalentrv.xpd.dim.notexist=22010031;400;维度不存在
apis.sptalentrv.xpd.dim.rule.notexist=22010032;400;维度规则不存在
apis.sptalentrv.xpd.dim.rule.calc.empty=22010033;400;暂未配置计算规则
apis.sptalentrv.xpd.dim.rule.actv.perf.notexist=22010034;400;绩效活动或指标不存在
apis.sptalentrv.xpd.dim.rule.ref.notexist=22010035;400;数据来源的绩效活动不存在
apis.sptalentrv.xpd.dim.rule.calctype.error=22010036;400;计算方式设置有误，请检查
apis.sptalentrv.xpd.dim.rule.resulttype.error=22010037;400;结果类型与全局设置不一致，请重新生成规则
apis.sptalentrv.xpd.dim.rule.formula.empty=22010038;400;高级公式未配置
apis.sptalentrv.xpd.dim.rule.formula.error=22010039;400;高级公式配置有误
apis.sptalentrv.xpd.dim.rule.level.empty=22010040;400;分层规则未配置
apis.sptalentrv.xpd.dim.rule.level.result.empty=22010041;400;分层规则的绩效结果为空,请检查
apis.sptalentrv.xpd.dim.rule.level.result.error=22010042;400;分层规则的绩效结果有误,请检查
apis.sptalentrv.xpd.dim.rule.subdim.error=22010043;400;维度选择有误
apis.sptalentrv.xpd.dim.rule.subdim.weight.empty=22010044;400;维度权重未设置
apis.sptalentrv.xpd.dim.rule.calc.weight.sum.error=22010045;400;权重之和必须为100%
apis.sptalentrv.xpd.dim.rule.indicator.duplicate=22010046;400;指标不能重复选择
apis.sptalentrv.xpd.dim.rule.indicator.notexist=22010047;400;指标不存在
apis.sptalentrv.xpd.dim.rule.indicator.ref.empty=22010048;400;指标数据来源为空，请检查
apis.sptalentrv.xpd.dim.rule.indicator.weight.empty=22010049;400;指标权重未设置
apis.sptalentrv.xpd.dim.rule.indicator.calcmethod.error=22010050;400;计算逻辑有误
apis.sptalentrv.xpd.dim.rule.indicator.calcmethod.empty=22010051;400;计算逻辑为空，请检查
apis.sptalentrv.xpd.rule.level.maxlength.error=22010052;400;分层名称最多支持20个字
apis.sptalentrv.xpd.modelId.notEmpty=22010053;400;盘点项目未关联模型
apis.sptalentrv.xpd.dimids.empty=22010054;400;盘点项目中关联维度为空，不能创建校准会
apis.sptalentrv.xpd.rule.conf.dimids.min=22010055;400;维度组合最少选择2个
apis.sptalentrv.xpd.impt.grid.level.not.exist=22010056;400;维度规则还未设置，分层不存在
apis.sptalentrv.xpd.score.exceed.max.value=22010057;400;分值超过分制的最大值


apis.sptalentrv.user.gradeLevel.length.200=22010080;400;职等长度不能超200
apis.sptalentrv.user.profCerts.length.200=22010081;400;职业资格证书长度不能超200

apis.sptalentrv.cali.importerror=2183026;400;校准导入模板错误
apis.sptalentrv.cali.import.no.username=2183027;400;校准会导入用户不能为空

apis.sptalentrv.cali.user.import.error.no.user=2183030;400;校准人员不存在
apis.sptalentrv.cali.user.import.error.no.dims=2183031;400;校准维度不存在
apis.sptalentrv.cali.user.import.error.no.result=2183032;400;人员校准结果不存在
apis.sptalentrv.cali.dimCombId.not.blank=2183033;400;维度组合ID不能为空
apis.sptalentrv.cali.not.exite=2183034;400;校准会不存在
apis.sptalentrv.cali.user.import.error.no.levels=2183035;400;分层结果不存在
apis.sptalentrv.cali.user.import.error.no.indicators=2183036;400;指标不存在
apis.sptalentrv.cali.meet.id.is.blank=2183037;400;校准会ID不能为空