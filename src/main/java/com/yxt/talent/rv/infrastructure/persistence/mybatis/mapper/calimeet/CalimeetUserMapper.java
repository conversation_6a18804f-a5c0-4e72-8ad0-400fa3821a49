package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.calimeet.dto.*;
import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetLargeChangePersonnelQry;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliMeetUserQuery;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliResult4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetUserPO;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CalimeetUserMapper extends CommonMapper<CalimeetUserPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetUserPO record);

    CalimeetUserPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetUserPO record);

    int updateBatch(@Param("list") List<CalimeetUserPO> list);

    int batchInsert(@Param("list") List<CalimeetUserPO> list);

    List<CaliMeetUserStatisticDTO> getCountByMeetIds(@Param("orgId") String orgId,
            @Param("caliMeetIds") List<String> caliMeetIds);

    default CaliMeetUserStatisticDTO getCountByMeetId(@Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId) {
        return getCountByMeetIds(orgId, List.of(caliMeetId)).stream().findFirst()
                .orElse(new CaliMeetUserStatisticDTO(caliMeetId, 0));
    }

    CaliMeetGeneralOverviewVO selectOverviewStatistic(String orgId, @NotNull String caliMeetId);

    int countLargeChangePeopleNumber(String orgId, @NotNull String caliMeetId, int threshold);

    /**
     * 获取校准前九宫格用户分布数据
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param xpdId      盘点项目ID
     * @param dimCombId  维度组合ID
     * @return 九宫格用户分布数据
     */
    List<CaliMeetGridItemVO> getBeforeCalibrationDistribution(@Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId, @Param("xpdId") String xpdId, @Param("dimCombId") String dimCombId);

    /**
     * 获取校准后九宫格用户分布数据
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param xpdId      盘点项目ID
     * @param dimCombId  维度组合ID
     * @return 九宫格用户分布数据
     */
    List<CaliMeetGridItemVO> getAfterCalibrationDistribution(@Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId, @Param("xpdId") String xpdId, @Param("dimCombId") String dimCombId);

    /**
     * 获取校准幅度较大人员列表
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param xpdId      盘点项目ID
     * @param query      查询条件
     * @return 校准幅度较大人员列表
     */
    IPage<CaliMeetLargeChangePersonnelVO> listLargeChangePersonnel(IPage<CaliMeetLargeChangePersonnelVO> page,
            @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("xpdId") String xpdId,
            @Param("query") CaliMeetLargeChangePersonnelQry query, @Param("threshold") int threshold);

    /**
     * 统计校准幅度较大人员总数
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param xpdId      盘点项目ID
     * @param query      查询条件
     * @return 校准幅度较大人员总数
     */
    int countLargeChangePersonnel(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("xpdId") String xpdId, @Param("query") CaliMeetLargeChangePersonnelQry query,
            @Param("threshold") int threshold);

    /**
     * 根据条件查询校准人员列表
     *
     * @param page       分页参数
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @return 校准人员列表
     */
    IPage<CaliMeetUserVO> selectCaliMeetUsers(@Param("page") IPage<CaliMeetUserVO> page, @Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId, @Param("query") CaliMeetUserQuery query);

    List<CaliMeetUserVO> selectCaliMeetUsers(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("query") CaliMeetUserQuery query);

    /**
     * 查询未添加的校准人员列表（来自关联的盘点项目）
     *
     * @param page       分页参数
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @return 未添加的人员列表
     */
    IPage<CaliMeetUser4Add> listNotAddedUsers(@Param("page") IPage<CaliMeetUser4Add> page, @Param("orgId") String orgId,
            @Param("caliMeetId") String caliMeetId, @Param("query") SearchUdpScopeAuthQuery query);

    /**
     * 逻辑删除校准人员
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param id         校准人员ID
     * @param operatorId 操作人ID
     * @return 影响行数
     */
    int logicDeleteCaliMeetUser(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("id") String id, @Param("operatorId") String operatorId);

    /**
     * 批量逻辑删除校准人员
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param userIds    校准人员ID列表
     * @param operatorId 操作人ID
     * @return 影响行数
     */
    int batchLogicDeleteCaliMeetUsers(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("userIds") List<String> userIds, @Param("operatorId") String operatorId);

    /**
     * 检查用户是否已经存在于校准会中
     *
     * @param orgId      组织ID
     * @param caliMeetId 校准会ID
     * @param userIds    用户ID列表
     * @return 存在的用户数量
     */
    int checkUserExists(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("userIds") List<String> userIds);

    List<CaliUserDTO> getCaliMeetUsers(@Param("orgId") String orgId, @Param("caliMeetId") String calId);

    List<CaliUserIdDto> listIdByUserIds(@Param("orgId") String orgId, @Param("caliMeetId") String calId,
            @Param("userIds") List<String> userIds);

    void batchUpdateLatestRecord(List<CaliUserIdDto> list);

    List<String> getCaliMeetUserIds(@Param("orgId") String orgId, @Param("caliMeetId") String calId);

    List<String> getExistentUserIds(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId,
            @Param("userIds") List<String> userIds);

    List<CaliIndexNumDTO> findCaliCellIndexMsg(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId);

    int countByOrgIdAndCalimeetId(@Param("orgId") String orgId, @Param("calimeetId") String calimeetId);

    List<CaliUserIndexDTO> findUserIndex(@Param("orgId") String orgId, @Param("calimeetId") String calimeetId,
            @Param("query") CaliResult4Query query);

    IPage<CaliCellUserVO> findGridUserPage(IPage<CaliCellUserVO> requestPage, @Param("orgId") String orgId,
            @Param("query") CaliResult4Query query);


    List<CaliUserIndexDTO> findUserIndex(@Param("orgId") String orgId, @Param("calimeetId") String calimeetId);

    /**
     * 获取盘点项目校准概览统计信息
     *
     * @param orgId 组织ID
     * @param xpdId 盘点项目ID
     * @return 盘点项目校准概览统计信息
     */
    CaliMeetGeneralOverviewVO selectXpdOverviewStatistic(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    /**
     * 统计盘点项目校准幅度较大人员数量
     *
     * @param orgId     组织ID
     * @param xpdId     盘点项目ID
     * @param threshold 校准幅度阈值
     * @return 校准幅度较大人员数量
     */
    int countXpdLargeChangePersonnel(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
            @Param("threshold") int threshold);

    /**
     * 获取盘点项目校准前九宫格用户分布数据
     *
     * @param orgId     组织ID
     * @param xpdId     盘点项目ID
     * @param dimCombId 维度组合ID
     * @param gridType  宫格类型
     * @return 九宫格用户分布数据
     */
    List<CaliMeetGridItemVO> selectXpdGridBeforeCalibration(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
            @Param("dimCombId") String dimCombId, @Param("gridType") Integer gridType);

    /**
     * 获取盘点项目校准后九宫格用户分布数据
     *
     * @param orgId     组织ID
     * @param xpdId     盘点项目ID
     * @param dimCombId 维度组合ID
     * @param gridType  宫格类型
     * @return 九宫格用户分布数据
     */
    List<CaliMeetGridItemVO> selectXpdGridAfterCalibration(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
            @Param("dimCombId") String dimCombId, @Param("gridType") Integer gridType);

    /**
     * 获取盘点项目校准幅度较大人员列表
     *
     * @param page      分页参数
     * @param orgId     组织ID
     * @param xpdId     盘点项目ID
     * @param threshold 校准幅度阈值
     * @return 校准幅度较大人员列表
     */
    IPage<CaliMeetLargeChangePersonnelVO> listXpdLargeChangePersonnelPage(IPage<CaliMeetLargeChangePersonnelVO> page,
            @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("threshold") int threshold);

    CaliLastRecordUserDTO findLastRecord(@Param("orgId") String orgId, @Param("calimeetId") String calimeetId,
            @Param("userId") String userId);

    List<CalimeetUserPO> selectByIds(String orgId, List<String> ids);

    List<CaliMeetUserGroupDTO> statisticByCaliMeetIds(@Param("orgId") String orgId,
            @Param("calimeetIds") List<String> caliMeetIds);
}
