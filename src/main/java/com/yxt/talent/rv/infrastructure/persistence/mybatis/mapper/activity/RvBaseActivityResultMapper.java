package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity;

import com.yxt.talent.rv.application.xpd.common.dto.AomUserRefIdDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.BaseActivityResultPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface RvBaseActivityResultMapper extends CommonMapper<BaseActivityResultPO> {

    int insert(BaseActivityResultPO record);

    BaseActivityResultPO selectByPrimaryKey(Long id);

    int updateByPrimaryKey(BaseActivityResultPO record);

    int updateBatch(@Param("list") List<BaseActivityResultPO> list);

    int batchInsert(@Param("list") List<BaseActivityResultPO> list);

    /**
     *
     * @param orgId
     * @param actvId
     * @param itemId
     * @param userIds
     * @return
     */
    List<String> doneUserIdsByItemId(
        @Param("orgId") String orgId,
        @Param("actvId")String actvId,
        @Param("itemId")Long itemId,
        @Param("userIds")List<String> userIds);

    List<AomUserRefIdDto> doneRefIdsByUserId(
        @Param("orgId") String orgId,
        @Param("actvId")String actvId,
        @Param("userIds") Collection<String> userIds,
        @Param("refIds") Collection<String> refIds);

    /**
     *
     * @param orgId
     * @param actvType ActivityTypeEnum
     * @param actvId
     * @param userIds
     * @return
     */
    List<String> doneUserIds(
        @Param("orgId") String orgId,
        @Param("actvType")int actvType,
        @Param("actvId")String actvId,
        @Param("userIds")List<String> userIds);
}