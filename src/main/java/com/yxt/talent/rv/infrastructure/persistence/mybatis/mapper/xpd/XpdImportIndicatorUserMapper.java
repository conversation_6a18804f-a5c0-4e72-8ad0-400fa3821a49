package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yxt.talent.rv.application.xpd.common.dto.ImportIndicatorNumDto;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportDimNumDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.XpdImportUserDTO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportIndicatorUserPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdImportIndicatorUserMapper extends CommonMapper<XpdImportIndicatorUserPO> {

    int insert(XpdImportIndicatorUserPO record);

    int insertOrUpdate(XpdImportIndicatorUserPO record);

    int insertOrUpdateSelective(XpdImportIndicatorUserPO record);

    XpdImportIndicatorUserPO selectByPrimaryKey(String id);

    List<XpdIndicatorResultDto> queryByUserIds(
        @Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("importIds") Collection<String> importIds,
        @Param("userIds") Collection<String> userIds,
        @Param("indicatorIds") Collection<String> indicatorIds);

   List<ImportIndicatorNumDto> selectImportAct(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
       @Param("userId") String userId);

   List<XpdImportUserDTO> selectUserCount(@Param("orgId") String orgId,
       @Param("xpdId") String xpdId, @Param("importIds") List<String> importIds);

    List<XpdImportIndicatorUserPO> selectByUserIds(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("importId") String importId, @Param("userIds") List<String> userIds);

    IPage<XpdImportIndicatorUserPO> selectByUserIdsPage(@Param("page") IPage<XpdImportIndicatorUserPO> page, @Param("orgId") String orgId,
        @Param("xpdId") String xpdId, @Param("importId") String importId, @Param("userIds") List<String> userIds);

    int insertBatch(
        @Param("xpdImportIndicatorUserPOCollection") Collection<XpdImportIndicatorUserPO> xpdImportIndicatorUserPOCollection);

    void batchUpdate(
        @Param("list") Collection<XpdImportIndicatorUserPO> xpdImportIndicatorUserPOCollection);

    void deleteByImportIdAndUserId(@Param("orgId") String orgId,
        @Param("importId") String importId,
        @Param("userId") String userId,
        @Param("operator") String operator);


    void deleteByXpdId( @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("operator") String operator);


    void deleteByXpdIdAndImportIds( @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("operator") String operator,
        @Param("importIds") Collection<String> importIds);


    List<ImportDimNumDTO> findDimNum(@Param("orgId") String orgId,
        @Param("xpdId") String xpdId,
        @Param("userId") String userId);
}