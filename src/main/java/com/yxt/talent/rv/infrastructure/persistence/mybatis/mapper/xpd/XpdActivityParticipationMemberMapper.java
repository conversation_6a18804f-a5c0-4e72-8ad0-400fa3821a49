package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.entity.part.ActivityParticipationMember;
import com.yxt.talent.rv.controller.client.bizmgr.team.query.TeamScopeAuthClientQuery;
import com.yxt.talent.rv.controller.client.general.prj.viewobj.PrjSubUserClientVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjResultSubUserVO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.user.viewobj.XpdUserPrjVO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/15 14:39
 */
public interface XpdActivityParticipationMemberMapper {

    /**
     * 【新盘点】 用户端盘点结果【项目维度】-> 我管辖的人员 <br>
     * 查询某项目中我管辖的人员
     */
    @Nonnull
    IPage<PrjSubUserClientVO> selectMyScopeUserInPrj(Page<PrjSubUserClientVO> pageable,
                                                     @Nonnull @Param("orgId") String orgId,
                                                     @Param("projectId") String projectId,
                                                     @Param("keyword") String keyword,
                                                     @Nonnull @Param("search") TeamScopeAuthClientQuery search,
                                                     @Param("qwUserIds") List<String> qwUserIds);

    /**
     * 【新盘点】我的团队-人才盘点-学员详情
     * 查询学员参与的盘点项目
     */
    @Nonnull
    IPage<PrjResultSubUserVO> listPrjByUserId(Page<PrjResultSubUserVO> pageable,
                                              @Nonnull @Param("orgId") String orgId,
                                              @Param("userId") String userId);

    /**
     * 【新盘点】我的团队-人才盘点-学员详情
     * 查询学员参与的盘点项目
     */
    @Nonnull
    List<PrjResultSubUserVO> listPrjByUserIdNoPage(@Nonnull @Param("orgId") String orgId,
                                                    @Param("userId") String userId);

    List<XpdUserPrjVO> findPrjListByUserId(
        @Nonnull @Param("orgId") String orgId,
        @Param("userId") String userId);

    long countByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    long countByXpdId4Query(@Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("query") XpdResultQuery query,
        @Param("allUserIds")  List<String> allUserIds);

    ActivityParticipationMember selectByXpdIdAndUserId(
        @Param("orgId") String orgId, @Param("xpdId") String xpdId, @Param("userId") String userId);

    List<ActivityParticipationMember> selectByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<ActivityParticipationMember> selectByXpdIds(String orgId, List<String> xpdIds);
}
