package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.yxt.talent.rv.application.xpd.common.dto.CalimeetDimResultDto;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetRecordPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CalimeetRecordMapper extends CommonMapper<CalimeetRecordPO> {
    int deleteByPrimaryKey(String id);

    int insert(CalimeetRecordPO record);

    CalimeetRecordPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(CalimeetRecordPO record);

    int updateBatch(@Param("list") List<CalimeetRecordPO> list);

    int batchInsert(@Param("list") List<CalimeetRecordPO> list);

    @Select("""
    select ur.suggestion,ur.reason,ur.cali_details,ur.result_details from rv_calimeet_user u
    join rv_calimeet_record ur on ur.id = u.latest_record_id
    where u.org_id = #{orgId} and u.calimeet_id = #{caliMeetId} and u.user_id = #{userId} and u.deleted = 0 limit 1
    """)
    CalimeetDimResultDto getLastDetail(@Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userId") String userId);

    int deleteByUserIds(
        @Param("orgId") String orgId, @Param("caliMeetId") String caliMeetId, @Param("userIds") List<String> userIds,
        @Param("operatorId") String operatorId);

    CalimeetDimResultDto getRecordDetail(@Param("orgId") String orgId, @Param("id") String id);

}