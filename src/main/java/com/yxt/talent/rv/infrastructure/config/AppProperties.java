package com.yxt.talent.rv.infrastructure.config;

import com.yxt.spsdk.logsave.LogSaveConfig;
import jakarta.annotation.PostConstruct;
import lombok.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "sprv")
public class AppProperties {

    private static final Logger log = LoggerFactory.getLogger(AppProperties.class);
    /**
     * 是否关闭dmp计算时查询过来的用户标签数据缓存，默认不关闭缓存
     */
    private boolean closeUserLabelCache = false;

    private String demoOrgId;

    /**
     * 盘点校准会创建时和开始时给参与人发送消息时的跳转地址
     */
    private String caliMeetMsgUrl;

    /**
     * 盘点校准会创建时和开始时给参与人发送待办消息时的跳转地址
     */
    private String caliMeetTodoUrl;

    /**
     * 是否开启审计日志
     */
    private boolean auditLogEnabled;

    /**
     * 一次查询最大值
     */
    private Long maxLimit = 1000L;

    /**
     * 人才发展内部日志告警系统
     */
    @NestedConfigurationProperty
    private SysLogConfig logConfig = new SysLogConfig();

    /**
     * 云端盘点项目下发的测评是否自动下发报告，默认不下发
     */
    private int autoIssueReport = 0;

    /**
     * 机构复制时，默认外键表映射key，用户动态配置返回给其他服务的id映射
     */
    private List<String> idMapKeys;

    @NestedConfigurationProperty
    private AiBox aibox;

    @NestedConfigurationProperty
    private TianHe tianHe;

    @NestedConfigurationProperty
    private Jwt jwt;

    private Map<String, String> gridColorMap = new HashMap<>();

    // 对应 sprv.gridThirdLevelColor
    // 外层 Map 的键是宫格类型 (0-四宫格, 1-九宫格, 2-16宫格) 对标 com.yxt.talent.rv.application.xpd.common.enums.GridTypeEnum
    // 内层 Map 的键是颜色索引 (1-, 2, 3, 4)，值是颜色代码
    private Map<Integer, List<String>> gridThirdLevelColor = new HashMap<>();

    @PostConstruct
    public void init() {
        log.debug("LOG21203:{}", gridThirdLevelColor);
        if (gridThirdLevelColor == null) {
            gridThirdLevelColor = new HashMap<>();
        }
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    public static class AiBox {
        private String baseUrl;
        private String appId;
        private String utilAppId; // 专门用于生成标题的agent
        private String ak;
        private String sk;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    public static class TianHe {
        /**
         * 授权码模式
         */
        public static final String GRANT_TYPE = "authorization_code";
        private String grantType = GRANT_TYPE;
        private String baseUrl;
        private String appId;
        private String ak;
        private String sk;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    public static class SysLogConfig {
        public static final String DEFAULT_DING_ROBOT_URL =
                "https://oapi.dingtalk.com/robot/send?access_token=65301cfac11f009bf8765d178efc5358f3325ed1decefce7bf079a3b772bfded";
        /**
         * 是否开启审计日志
         */
        private boolean enabled;

        /**
         * 默认只打印com.yxt包下面的类产生的异常
         */
        private int saveStyle = LogSaveConfig.SAVE_STYLE_FILTER_BIZ;

        /**
         * 业务中有报错发生时，推送给钉钉机器人的url
         */
        private String dingRobotUrl = DEFAULT_DING_ROBOT_URL;

        /**
         * 订阅人
         */
        private String observer;

        /**
         * 需要日志拦截的业务包
         */
        private String bizLogPkg = "com.yxt.talent";
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    public static class Jwt {
        private String secret = "2/TRdKkiyHZuy-imWA*44iJM(1$z_viF";
        private int timeout = 1209600;
    }
}
