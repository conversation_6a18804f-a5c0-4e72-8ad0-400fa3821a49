package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 盘点宫格分级标准
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_grid_level")
public class XpdGridLevelPO {
    private static final Logger log = LoggerFactory.getLogger(XpdGridLevelPO.class);
    /**
    * id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID, idMapKey = SprvDemoOrgCopyConstants.XPD_GRID_LEVEL_ID)
    private String id;

    /**
    * 机构ID, 全局模板存00000000-0000-0000-0000-000000000000
    */
    private String orgId;

    /**
    * 项目ID, 机构模板存00000000-0000-0000-0000-000000000000
    */
    private String xpdId;

    /**
    * 宫格ID, 指向rv_xpd_grid.id
    */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.XPD_GRID_ID)
    private String gridId;

    /**
    * 分级名称
    */
    private String levelName;

    /**
    * 分级名称国际化
    */
    private String levelNameI18n;

    /**
    * 维度分层编号。九宫格(0-默认，1-低，2-中，3-高)； 四宫格(0-默认，1-低，2-高)； 十六宫格(0-默认，1-较差，2-一般，3-良好，4-优秀)
    */
    private Integer orderIndex;

    /**
    * 0:未删除 1:已删除
    */
    private Integer deleted;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 创建人ID
    */
    private String createUserId;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;

    /**
    * 更新人ID
    */
    private String updateUserId;

    /**
     * 九宫格中展示第三维度时，员工姓名卡片的边框颜色
     */
    private String thirdDimColor;

    public String decideThirdDimColor(XpdGridPO xpdGrid, XpdGridLevelPO xpdGridLevelPO, AppProperties appProperties) {
        if (StringUtils.isBlank(xpdGridLevelPO.getThirdDimColor())) {
            List<String> levelColors = appProperties.getGridThirdLevelColor().get(xpdGrid.getGridType());
            if (CollectionUtils.isNotEmpty(levelColors)) {
                return levelColors.get(xpdGridLevelPO.getOrderIndex() - 1);
            }
            log.warn("LOG21213:{}, {}", xpdGrid.getGridType(), xpdGridLevelPO.getOrderIndex());
        }
        return xpdGridLevelPO.getThirdDimColor();
    }
}