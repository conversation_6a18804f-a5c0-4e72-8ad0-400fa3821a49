package com.yxt.talent.rv.infrastructure.service.auth;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Auth {
    /**
     * 功能码列表，与内部平台权限一致，必须指定
     */
    String[] codes() default {};
    
    /**
     * 否允许匿名访问，默认不允许
     */
    boolean allowAnonymous() default false;
    
    /**
     * 是否跳过权限校验，默认不跳过
     */
    boolean skipCheck() default false;
} 