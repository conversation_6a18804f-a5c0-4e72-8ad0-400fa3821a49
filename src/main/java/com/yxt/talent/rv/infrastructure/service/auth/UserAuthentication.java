package com.yxt.talent.rv.infrastructure.service.auth;

import lombok.Data;

/**
 * 用户认证信息，用于控制器方法参数注入
 */
@Data
public class UserAuthentication {
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 组织ID
     */
    private String orgId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 全名
     */
    private String fullName;
    
    /**
     * 是否管理员
     */
    private Boolean isAdmin;
} 