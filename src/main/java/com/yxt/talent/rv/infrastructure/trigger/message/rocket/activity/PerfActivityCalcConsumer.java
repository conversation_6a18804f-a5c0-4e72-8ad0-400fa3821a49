package com.yxt.talent.rv.infrastructure.trigger.message.rocket.activity;

import com.alibaba.fastjson.JSON;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.application.activity.ActivityCalcComponent;
import com.yxt.talent.rv.application.activity.PerfActivityService;
import com.yxt.talent.rv.application.activity.dto.PerfActivityCalcMqDTO;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.CalcLogTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PERF_ACTIVITY_CALC;

/**
 * <AUTHOR>
 * @since 2024/12/30
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(consumerGroup = CONSUMER_GROUP_PREFIX         + TOPIC_PERF_ACTIVITY_CALC, topic = TOPIC_PERF_ACTIVITY_CALC, consumeThreadNumber = 1, consumeTimeout = 30)
public class PerfActivityCalcConsumer implements RocketMQListener<PerfActivityCalcMqDTO> {
    private final PerfActivityService perfActivityService;
    private final ActivityCalcComponent activityCalcComponent;

    @Override
    public void onMessage(PerfActivityCalcMqDTO message) {
        log.info("PerfActivityCalcConsumer param ={}", JSON.toJSONString(message));
        try {
            perfActivityService.calUserPerf(message.getOrgId(), message.getOptUserId(),
                    message.getPerfProfId());
        } catch (Exception e) {
            log.warn("PerfActivityCalcConsumer param ={},error=", JSON.toJSONString(message), e);
        } finally {
            try {
                // 计算完成，更新状态为已完成
                activityCalcComponent.completeCalculate(
                    message.getOrgId(), message.getPerfProfId(), CalcLogTypeEnum.ACT_PREF.getCode());
            } catch (Exception e) {
                log.warn("LOG20363:", e);
            }
        }
    }
}
