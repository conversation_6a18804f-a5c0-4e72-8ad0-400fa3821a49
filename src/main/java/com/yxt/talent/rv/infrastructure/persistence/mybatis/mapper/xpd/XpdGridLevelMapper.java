package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface XpdGridLevelMapper extends CommonMapper<XpdGridLevelPO> {
    int deleteByPrimaryKey(String id);

    int insert(XpdGridLevelPO record);

    int insertOrUpdate(XpdGridLevelPO record);

    int insertOrUpdateSelective(XpdGridLevelPO record);

    XpdGridLevelPO selectByPrimaryKey(String id);

    int updateByPrimaryKey(XpdGridLevelPO record);

    int updateBatch(@Param("list") List<XpdGridLevelPO> list);

    int batchInsert(@Param("list") List<XpdGridLevelPO> list);

    int batchInsertOrUpdate(@Param("list") List<XpdGridLevelPO> list);

    List<XpdGridLevelPO> listByGridIds(
        @Param("orgId") String orgId, @Param("gridIds") Collection<String> gridIds);

    List<XpdGridLevelPO> listByGridId(@Param("orgId") String orgId, @Param("gridId") String gridId);

    List<XpdGridLevelPO> listByGridIdDesc(
        @Param("orgId") String orgId, @Param("gridId") String gridId);

    List<XpdGridLevelPO> listByGridIdReverse(
        @Param("orgId") String orgId, @Param("gridId") String gridId);

    List<XpdGridLevelPO> listByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId);

    void deleteGridLevel(
        @Param("orgId") String orgId, @Param("gridId") String gridId,
        @Param("userId") String userId);

    int insertBatch(@Param("coll") Collection<XpdGridLevelPO> coll);

    List<XpdGridLevelPO> listByOrgId(@Param("orgId") String orgId);

    void deleteByXpdId(
        @Param("orgId") String orgId, @Param("userId") String userId, @Param("xpdId") String xpdId);

    IPage<XpdGridLevelPO> listByXpdIdPage(
        Page<XpdGridLevelPO> pageParam, @Param("orgId") String orgId, @Param("xpdId") String xpdId);

    List<XpdGridLevelPO> selectByIds(@Param("ids") List<String> dimLayerIds);
}