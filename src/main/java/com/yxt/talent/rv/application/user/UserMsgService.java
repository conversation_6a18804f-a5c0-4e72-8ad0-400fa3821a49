package com.yxt.talent.rv.application.user;

import com.yxt.common.util.*;
import com.yxt.talent.rv.controller.talentcore.command.UserExtMsgCmd;
import com.yxt.talent.rv.controller.talentcore.viewobj.CareerHistoryVO;
import com.yxt.talent.rv.controller.talentcore.viewobj.RewardPunishmentHistoryVO;
import com.yxt.talent.rv.controller.talentcore.viewobj.UserBaseMsgVO;
import com.yxt.talent.rv.controller.talentcore.viewobj.UserExtMsgVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.EducationEnum;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.career.CareerHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.reward.RewardPunishmentHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.UserExtMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.CareerHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.RewardPunishmentHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserExtPO;
import com.yxt.udpfacade.bean.user.UserBean;
import com.yxt.udpfacade.bean.user.UserEduExpBean;
import com.yxt.udpfacade.bean.user.UserQueryRequest;
import com.yxt.udpfacade.bean.user.UserSpareItem;
import com.yxt.udpfacade.service.UdpFacade;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/4/8
 */
@Service
@RequiredArgsConstructor
public class UserMsgService {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final RewardPunishmentHistoryMapper rewardPunishmentHistoryMapper;
    private final UserExtMapper userExtMapper;
    private final CareerHistoryMapper careerHistoryMapper;
    private final UdpFacade udpFacade;


    public UserBaseMsgVO getUserBaseMsg(String orgId, String userId) {

        UdpLiteUserPO udpLiteUser = getUdpUser(orgId, userId);
        UserBaseMsgVO res = new UserBaseMsgVO();
        BeanCopierUtil.copy(udpLiteUser, res);
        UserQueryRequest userQueryRequest = new UserQueryRequest();
        userQueryRequest.setOrgId(orgId);
        userQueryRequest.setUserId(userId);
        userQueryRequest.setInclExtended(true);
        userQueryRequest.setInclEduExps(true);
        userQueryRequest.setInclSpare(true);
        UserBean userInfo = udpFacade.getUserInfo(userQueryRequest);
        // todo 调用udp接口获取
        if (userInfo != null) {
            List<UserSpareItem> spares = userInfo.getSpares();
            // 政治面貌
            if (CollectionUtils.isNotEmpty(spares)) {
                for (UserSpareItem spare : spares) {
                    if ("rt50Spare2".equals(spare.getSpareKey())) {
                        res.setPoliticalStatus(spare.getSpareValue());
                    }
                }
            }
            // 学历专业
            /*List<UserEduExpBean> eduExps = userInfo.getEduExps();
            List<String> majors = new ArrayList<>();
            List<String> educations = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(eduExps)) {
                for (UserEduExpBean eduExp : eduExps) {
                    educations.add(EducationEnum.getByCode(eduExp.getEducation()));
                    majors.add(eduExp.getMajor());
                }
                res.setMajor(String.join("、", majors));
                res.setEducation(String.join("、", educations));
            }*/

        }
        return res;
    }

    private UdpLiteUserPO getUdpUser(String orgId, String userId) {
        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectByUserId(orgId, userId);
        Validate.isNotNull(udpLiteUserPO, ExceptionKeys.USER_NOT_EXISTED);
        return udpLiteUserPO;
    }

    public UserExtMsgVO getUserExtMsg(String orgId,  String userId){
        UdpLiteUserPO udpLiteUser = udpLiteUserMapper.selectByUserId(orgId, userId);
        UserExtMsgVO result = new UserExtMsgVO();
        result.setThirdUserId(udpLiteUser.getThirdUserId());
        String thirdUserId = udpLiteUser.getThirdUserId();

        List<UserExtPO> userExtList = userExtMapper.selectByOrgIdAndUserId(orgId, userId);
        if (CollectionUtils.isNotEmpty(userExtList)) {
            UserExtPO userExt = userExtList.get(0);
            result.setManager(userExt.getManager());
            result.setGradeLevel(userExt.getGradeLevel());
            result.setProfCerts(userExt.getProfCerts());
            result.setResidenceAddress(userExt.getResidenceAddress());
        }
        List<CareerHistoryPO> careerHistoryList = new ArrayList<>();

        List<CareerHistoryPO> careerHistoryUserList = careerHistoryMapper.selectByUserId(orgId, userId);
        if (CollectionUtils.isNotEmpty(careerHistoryUserList)) {
            careerHistoryList.addAll(careerHistoryUserList);
        }
        if (StringUtils.isNotBlank(udpLiteUser.getThirdUserId())) {
            List<CareerHistoryPO> careerHistoryThirdList =
                careerHistoryMapper.selectByThirdUserId(orgId, udpLiteUser.getThirdUserId());
            if (CollectionUtils.isNotEmpty(careerHistoryThirdList)) {
                careerHistoryList.addAll(careerHistoryThirdList);
            }

        }
        careerHistoryList = careerHistoryList.stream()
            .collect(Collectors.toMap(
                CareerHistoryPO::getId, // 按照 id 作为 key
                item -> item,                     // value 是元素本身
                (existing, replacement) -> existing // 如果 key 冲突，保留第一个出现的元素
            ))
            .values()
            .stream()
            .toList();

        careerHistoryList = careerHistoryList.stream()
            .sorted(Comparator.comparing(CareerHistoryPO::getOccurrenceTime, Comparator.nullsLast(Comparator.reverseOrder())))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(careerHistoryList)) {
            List<CareerHistoryVO> realCareerHistoryList = new ArrayList<>();
            careerHistoryList.forEach(x->{
                CareerHistoryVO careerHistory = new CareerHistoryVO();
                careerHistory.setId(x.getId());
                careerHistory.setThirdUserId(thirdUserId);
                careerHistory.setThirdCareerHistoryId(x.getThirdCareerHistoryId());
                careerHistory.setThirdDeptName(x.getThirdDeptName());
                careerHistory.setThirdPositionName(x.getThirdPositionName());
                careerHistory.setThirdJobgradeName(x.getThirdJobgradeName());
                careerHistory.setActionName(x.getActionName());
                careerHistory.setOccurrenceTime(x.getOccurrenceTime());
                realCareerHistoryList.add(careerHistory);
            });
            result.setCareerHistoryList(realCareerHistoryList);
        }
        List<RewardPunishmentHistoryPO> rewardPunishmentHistoryList = new ArrayList<>();
        List<RewardPunishmentHistoryPO> rewardPunishmentHistoryPOS =
            rewardPunishmentHistoryMapper.selectByOrgIdAndUserId(orgId, userId);
        if (CollectionUtils.isNotEmpty(rewardPunishmentHistoryPOS)) {
            rewardPunishmentHistoryList.addAll(rewardPunishmentHistoryPOS);
        }
        if (StringUtils.isNotBlank(udpLiteUser.getThirdUserId())) {
            List<RewardPunishmentHistoryPO> rewardPunishmentThirdHistoryList =
                rewardPunishmentHistoryMapper.selectByOrgIdAndThirdUserId(orgId, thirdUserId);
            if (CollectionUtils.isNotEmpty(rewardPunishmentThirdHistoryList)) {
                rewardPunishmentHistoryList.addAll(rewardPunishmentThirdHistoryList);
            }
        }
        // 根据id去重
        rewardPunishmentHistoryList = rewardPunishmentHistoryList.stream()
            .collect(Collectors.toMap(
                RewardPunishmentHistoryPO::getId, // 按照 id 作为 key
                item -> item,                     // value 是元素本身
                (existing, replacement) -> existing // 如果 key 冲突，保留第一个出现的元素
            ))
            .values()
            .stream()
            .toList();

        rewardPunishmentHistoryList = rewardPunishmentHistoryList.stream()
            .sorted(Comparator.comparing(RewardPunishmentHistoryPO::getAcqTime, Comparator.nullsLast(Comparator.reverseOrder())))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rewardPunishmentHistoryList)) {
            List<RewardPunishmentHistoryVO> rewardPunishmentHistorys = new ArrayList<>();
            rewardPunishmentHistoryList.forEach( x ->{
                RewardPunishmentHistoryVO rewardPunishmentHistory = new RewardPunishmentHistoryVO();
                rewardPunishmentHistory.setId(x.getId());
                rewardPunishmentHistory.setThirdUserId(thirdUserId);
                rewardPunishmentHistory.setRpType(x.getRpType());
                rewardPunishmentHistory.setRpName(x.getRpName());
                rewardPunishmentHistory.setAcqTime(x.getAcqTime());
                rewardPunishmentHistory.setPubFrom(x.getPubFrom());
                rewardPunishmentHistorys.add(rewardPunishmentHistory);

            });
            result.setRewardPunishmentHistoryList(rewardPunishmentHistorys);

        }

        return result;

    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void editUserExtMsg(String orgId, UserExtMsgCmd cmd){
        String userId = cmd.getUserId();
        UdpLiteUserPO udpLiteUser = udpLiteUserMapper.selectByUserId(orgId, userId);
        if (udpLiteUser == null) {
            return;
        }
        String thirdUserId = udpLiteUser.getThirdUserId();
        List<UserExtPO> realUserExtList = new ArrayList<>();
        dealUserExt(orgId, cmd, thirdUserId, realUserExtList, userId);

        List<String> deletedHistoryIds = new ArrayList<>();
        List<CareerHistoryPO> realCareerHistoryList = new ArrayList<>();
        dealCareerHistory(orgId, cmd, thirdUserId, userId, realCareerHistoryList, deletedHistoryIds);

        List<String> deletedRewardIds = new ArrayList<>();
        List<RewardPunishmentHistoryPO> realRewardPunishmentHistoryList = new ArrayList<>();

        dealReward(orgId, cmd, thirdUserId, realRewardPunishmentHistoryList, deletedRewardIds, userId);
        if (CollectionUtils.isNotEmpty(realUserExtList)) {
            userExtMapper.insertOrUpdateBatch(realUserExtList);
        }

        if (CollectionUtils.isNotEmpty(realCareerHistoryList)) {
            careerHistoryMapper.insertOrUpdateBatch(realCareerHistoryList);
        }

        if (CollectionUtils.isNotEmpty(deletedHistoryIds)) {
            careerHistoryMapper.deleteByThirdUserIdAndThirdCareerHistoryIds(orgId,deletedHistoryIds);
        }

        if (CollectionUtils.isNotEmpty(realRewardPunishmentHistoryList)) {
            rewardPunishmentHistoryMapper.insertOrUpdateBatch(realRewardPunishmentHistoryList);
        }
        if (CollectionUtils.isNotEmpty(deletedRewardIds)) {
            rewardPunishmentHistoryMapper.deleteByIds(orgId, deletedRewardIds);

        }

    }

    private void dealReward(
        String orgId, UserExtMsgCmd cmd, String thirdUserId,
        List<RewardPunishmentHistoryPO> realRewardPunishmentHistoryList, List<String> deletedRewardIds,
        String userId) {
        // 已经存在的
        List<RewardPunishmentHistoryPO> rewardPunishmentHistoryList = new ArrayList<>();
        if (StringUtils.isNotEmpty(thirdUserId)) {
            List<RewardPunishmentHistoryPO> rewardPunishmentThirdHistoryList =
                rewardPunishmentHistoryMapper.selectByOrgIdAndThirdUserId(orgId, thirdUserId);
            if (CollectionUtils.isNotEmpty(rewardPunishmentThirdHistoryList)) {
                rewardPunishmentHistoryList.addAll(rewardPunishmentThirdHistoryList);
            }
        }
        List<RewardPunishmentHistoryPO> rewardPunishmentHistoryUserList =
            rewardPunishmentHistoryMapper.selectByOrgIdAndUserId(orgId, userId);
        if (CollectionUtils.isNotEmpty(rewardPunishmentHistoryUserList)) {
            rewardPunishmentHistoryList.addAll(rewardPunishmentHistoryUserList);
        }

        List<RewardPunishmentHistoryVO> inRewardPunishmentHistoryList = cmd.getRewardPunishmentHistoryList();
        if (CollectionUtils.isNotEmpty(rewardPunishmentHistoryList)) {
            List<String> existIds = rewardPunishmentHistoryList.stream().map(RewardPunishmentHistoryPO::getId).toList();
            if (CollectionUtils.isEmpty(inRewardPunishmentHistoryList)) {
                deletedRewardIds.addAll(existIds);
            } else {
                List<String> inRewardIds = inRewardPunishmentHistoryList.stream().map(RewardPunishmentHistoryVO::getId)
                    .filter(StringUtils::isNotEmpty)
                    .toList();
                deletedRewardIds.addAll(existIds.stream().filter( x-> !inRewardIds.contains(x)).toList());
            }
        }
        Map<String, RewardPunishmentHistoryPO> rewardMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(inRewardPunishmentHistoryList)) {
            rewardMap = StreamUtil.list2map(rewardPunishmentHistoryList, RewardPunishmentHistoryPO::getId);
        }
        for (RewardPunishmentHistoryVO vo : inRewardPunishmentHistoryList) {
            String rewardId = vo.getId();
            if (StringUtils.isNotBlank(rewardId) && rewardMap.containsKey(rewardId)) {
                RewardPunishmentHistoryPO rewardPunishmentHistory = rewardMap.get(rewardId);
                rewardPunishmentHistory.setRpName(vo.getRpName());
                rewardPunishmentHistory.setRpType(vo.getRpType());
                rewardPunishmentHistory.setAcqTime(vo.getAcqTime());
                rewardPunishmentHistory.setPubFrom(vo.getPubFrom());
                rewardPunishmentHistory.setUpdateTime(LocalDateTime.now());
                rewardPunishmentHistory.setUserId(userId);
                realRewardPunishmentHistoryList.add(rewardPunishmentHistory);
            } else {
                //rewardId = MD5.create().digestHex(orgId + thirdUserId + vo.getRpType() + vo.getAcqTime() + vo.getRpName());
                rewardId = ApiUtil.getUuid();
                RewardPunishmentHistoryPO rewardPunishmentHistory = new RewardPunishmentHistoryPO();
                rewardPunishmentHistory.setId(rewardId);
                rewardPunishmentHistory.setOrgId(orgId);
                rewardPunishmentHistory.setThirdUserId(thirdUserId);
                rewardPunishmentHistory.setRpType(vo.getRpType());
                rewardPunishmentHistory.setRpName(vo.getRpName());
                rewardPunishmentHistory.setAcqTime(vo.getAcqTime());
                rewardPunishmentHistory.setPubFrom(vo.getPubFrom());
                rewardPunishmentHistory.setDeleted(0);
                rewardPunishmentHistory.setCreateTime(LocalDateTime.now());
                rewardPunishmentHistory.setUpdateTime(LocalDateTime.now());
                rewardPunishmentHistory.setUserId(userId);
                realRewardPunishmentHistoryList.add(rewardPunishmentHistory);
            }
        }
    }

    private void dealCareerHistory(
        String orgId, UserExtMsgCmd cmd, String thirdUserId, String userId,
        List<CareerHistoryPO> realCareerHistoryList, List<String> deletedHistoryIds) {

        List<CareerHistoryVO> inCareerHistoryList = cmd.getCareerHistoryList();
        List<CareerHistoryPO> careerHistoryList = new ArrayList<>();
        List<CareerHistoryPO> careerHistoryThirdList = careerHistoryMapper.selectByThirdUserId(orgId, thirdUserId);
        if (CollectionUtils.isNotEmpty(careerHistoryThirdList)) {
            careerHistoryList.addAll(careerHistoryThirdList);
        }
        List<CareerHistoryPO> careerHistoryUserList = careerHistoryMapper.selectByUserId(orgId, userId);
        if (CollectionUtils.isNotEmpty(careerHistoryUserList)) {
            careerHistoryList.addAll(careerHistoryUserList);
        }

        if (CollectionUtils.isNotEmpty(careerHistoryList)) {
            List<String> existHistoryIds = careerHistoryList.stream().map(CareerHistoryPO::getThirdCareerHistoryId).toList();

            if (CollectionUtils.isEmpty(inCareerHistoryList)) {
                deletedHistoryIds.addAll(existHistoryIds);
            } else {
                List<String> inHistiryIds = inCareerHistoryList.stream().map(CareerHistoryVO::getThirdCareerHistoryId)
                    .filter(StringUtils::isNotEmpty).toList();
                deletedHistoryIds.addAll(existHistoryIds.stream().filter(x-> !inHistiryIds.contains(x)).toList());
            }
        }
        Map<String, CareerHistoryPO> careerHistoryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(careerHistoryList)) {
            careerHistoryMap =
                StreamUtil.list2map(careerHistoryList, CareerHistoryPO::getId);
        }

        // 寻找删除的
        for (CareerHistoryVO vo : inCareerHistoryList) {
            String thirdCareerHistoryId = vo.getThirdCareerHistoryId();
            if (StringUtils.isBlank(thirdCareerHistoryId)) {
                thirdCareerHistoryId = ApiUtil.getUuid();
            }

            if (StringUtils.isBlank(vo.getId())) {
                CareerHistoryPO newCareerHistory = new CareerHistoryPO();
                BeanHelper.copyProperties(vo, newCareerHistory);
                newCareerHistory.setId(ApiUtil.getUuid());
                newCareerHistory.setOrgId(orgId);
                newCareerHistory.setThirdUserId(thirdUserId);
                newCareerHistory.setThirdCareerHistoryId(thirdCareerHistoryId);
                newCareerHistory.setDeleted(0);
                newCareerHistory.setCreateTime(LocalDateTime.now());
                newCareerHistory.setUpdateTime(LocalDateTime.now());
                newCareerHistory.setUserId(userId);
                realCareerHistoryList.add(newCareerHistory);
            } else {
                CareerHistoryPO careerHistoryPO = careerHistoryMap.get(vo.getId());
                if (careerHistoryPO != null) {
                    careerHistoryPO.setThirdDeptName(vo.getThirdDeptName());
                    careerHistoryPO.setThirdPositionName(vo.getThirdPositionName());
                    careerHistoryPO.setThirdJobgradeName(vo.getThirdJobgradeName());
                    careerHistoryPO.setActionName(vo.getActionName());
                    careerHistoryPO.setOccurrenceTime(vo.getOccurrenceTime());
                    careerHistoryPO.setUserId(userId);
                    careerHistoryPO.setUpdateTime(LocalDateTime.now());
                    realCareerHistoryList.add(careerHistoryPO);
                } else {
                    CareerHistoryPO newCareerHistory = new CareerHistoryPO();
                    BeanHelper.copyProperties(vo, newCareerHistory);
                    newCareerHistory.setId(ApiUtil.getUuid());
                    newCareerHistory.setOrgId(orgId);
                    newCareerHistory.setThirdUserId(thirdUserId);
                    newCareerHistory.setThirdCareerHistoryId(thirdCareerHistoryId);
                    newCareerHistory.setDeleted(0);
                    newCareerHistory.setCreateTime(LocalDateTime.now());
                    newCareerHistory.setUpdateTime(LocalDateTime.now());
                    newCareerHistory.setUserId(userId);
                    realCareerHistoryList.add(newCareerHistory);
                }

            }
        }
    }

    private void dealUserExt(
        String orgId, UserExtMsgCmd cmd, String thirdUserId, List<UserExtPO> realUserExtList, String userId) {
        List<UserExtPO> userExtList = userExtMapper.selectByOrgIdAndUserId(orgId, userId);
        Map<String, UserExtPO> userExtMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userExtList)) {
            userExtMap = StreamUtil.list2map(userExtList, UserExtPO::getId);
        }


        if (CollectionUtils.isNotEmpty(userExtList)) {
            UserExtPO userExt = userExtList.get(0);
            userExt.setManager(cmd.getManager());
            userExt.setGradeLevel(cmd.getGradeLevel());
            userExt.setProfCerts(cmd.getProfCerts());
            userExt.setResidenceAddress(cmd.getResidenceAddress());
            //EntityUtil.setUpdatedInfo(userId, userExt);
            realUserExtList.add(userExt);
        } else {
            UserExtPO userExt = new UserExtPO();
            userExt.setId(ApiUtil.getUuid());
            userExt.setThirdUserId(thirdUserId);
            userExt.setOrgId(orgId);
            userExt.setEnabled(1);
            userExt.setUserId(userId);
            userExt.setManager(cmd.getManager());
            //userExt.setKeyPosition();
            userExt.setProfCerts(cmd.getProfCerts());
            userExt.setResidenceAddress(cmd.getResidenceAddress());
            userExt.setGradeLevel(cmd.getGradeLevel());
            userExt.setDeleted(0);
            //EntityUtil.setCreateInfo(userId, userExt);
            realUserExtList.add(userExt);
        }


    }


}
