package com.yxt.talent.rv.application.calimeet.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16
 */
@Data
public class CaliRecordDimExportDTO {

    private String fullname;

    private String username;

    private String statusStr;

    private String deptName;

    private String positionName;

    private String gradeName;

    private String dimCombName;

    // 校准幅度
    private String caliShiftStr;

    @Schema(description = "校准前宫格")
    private String originalCellIndexName;

    private Integer originalCellIndex;

    @Schema(description = "校准后宫格")
    private String cellIndexName;

    // 校准前 1
    private String originalDimLevel1;

    // 校准后 1
    private String dimLevel1;

    // 校准前 2
    private String originalDimLevel2;

    // 校准后 2
    private String dimLevel2;

    @Schema(description = "校准人")
    private String caliUserName;

    @Schema(description = "校准时间")
    private String caliTimeStr;

    // 校准原因
    private String reason;

    private String suggestion;
}
