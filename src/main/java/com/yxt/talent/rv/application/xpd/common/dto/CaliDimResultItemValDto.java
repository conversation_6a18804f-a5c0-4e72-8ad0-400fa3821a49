package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CaliDimResultItemValDto {
    @Schema(description = "宫格分层id, rv_xpd_grid_level.id")
    private String gridLevelId;
    @Schema(description = "分值, 包括绩效得分")
    private BigDecimal scoreValue;
    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;
    @Schema(description = "是否达标")
    private Integer qualified;
    @Schema(description = "绩效活动等级id")
    private String perfLevelId;
}
