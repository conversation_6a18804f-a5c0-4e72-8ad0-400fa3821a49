package com.yxt.talent.rv.application.xpd.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Schema(description = "项目详情返回")
public class RvProject4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="新盘点ID")
    private String xpdId;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="项目名称;名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="盘点分类")
    private String categoryid;
    @Schema(description="盘点分类展示信息")
    @JsonProperty("@categoryid")
    private AmSlDrawer4RespDTO categoryid__Record;
    @Schema(description="项目负责人;按照业务需求,返回应用的实体字段")
    @JsonProperty("@projmanager")
    private AmSlDrawer4RespDTO projmanager;
    @Schema(description="盘点场景;按照业务需求,返回应用的实体字段")
    @JsonProperty("@sceneid")
    private AmSlDrawer4RespDTO sceneid;
    @Schema(description="盘点模型;按照业务需求,返回应用的实体字段")
    @JsonProperty("@modelid")
    private AmSlDrawer4RespDTO modelid;
    @Schema(description="盘点目标")
    private String projdescription;
    @Schema(description="计划开始时间")
    private Date starttime;
    @Schema(description="计划结束时间")
    private Date endtime;
    @Schema(description="自动结束项目")
    private Integer autoend;
    @Schema(description="开启项目审核")
    private Integer auditenabled;
    @Schema(description="项目状态")
    private String projstatus;
    @Schema(description="参与人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private long participationId;
    @Schema(description="审核状态")
    private String auditStatus;
    @Schema(description="起止时间")
    private String startEndTime;
    @Schema(description = "是否计算中: 0-否 1-是")
    private int calcStatus;
}
