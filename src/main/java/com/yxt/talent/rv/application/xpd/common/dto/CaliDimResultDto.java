package com.yxt.talent.rv.application.xpd.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CaliDimResultDto {
    //保存数据库不需要
    @JsonIgnore
    private String userId;
    @Schema(description = "冗余的人才标准的维度id")
    private String sdDimId;
    @Schema(description = "计算的宫格分层id, rv_xpd_grid_level.id")
    private String gridLevelId;
    @Schema(description = "分值, 包括绩效得分")
    private BigDecimal scoreValue;
    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;
}
