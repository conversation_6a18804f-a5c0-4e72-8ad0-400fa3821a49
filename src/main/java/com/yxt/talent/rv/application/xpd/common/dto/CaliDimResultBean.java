package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CaliDimResultBean {
    //是否计算出新结果
    private boolean newResult;
    private String userId;
    @Schema(description = "冗余的人才标准的维度id")
    private String sdDimId;
    @Schema(description = "计算的宫格分层id, rv_xpd_grid_level.id")
    private String gridLevelId;
    @Schema(description = "分值, 包括绩效得分")
    private BigDecimal scoreValue;
    @Schema(description = "达标率")
    private BigDecimal qualifiedPtg;

    @Schema(description = "盘点宫格分层")
    private String xpdGridLevelId;
    @Schema(description = "盘点分值, 包括绩效得分")
    private BigDecimal xpdScoreValue;
    @Schema(description = "盘点达标率")
    private BigDecimal xpdQualifiedPtg;
}
