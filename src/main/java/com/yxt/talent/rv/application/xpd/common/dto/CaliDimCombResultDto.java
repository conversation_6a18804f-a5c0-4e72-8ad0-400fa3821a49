package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CaliDimCombResultDto {
    private String combId;
    @Schema(description = "宫格组合名")
    private String combName;
    @Schema(description = "盘点格子编号")
    private Integer xpdCellIndex;
    private String xSdDimId;
    private String xSdDimName;
    private String ySdDimId;
    private String ySdDimName;
    @Schema(description = "校准格子编号")
    private Integer caliCellIndex;
    @Schema(description = "校准幅度")
    private Integer caliGap;
}
