package com.yxt.talent.rv.application.xpd.xpd;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.ApplicationCommandService;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.common.pojo.api.CommonList;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import com.yxt.talent.rv.application.prj.prj.dto.PrjCompletedUserDTO;
import com.yxt.talent.rv.application.user.UserTransferComponent;
import com.yxt.talent.rv.application.xpd.common.dto.XpdUserCntDTO;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptPrjUserClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptXpdStatisticsClientVO;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjOverviewResultDTO;
import com.yxt.talent.rv.controller.manage.xpd.result.query.XpdResultQuery;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdTableResultVO;
import com.yxt.talent.rv.controller.manage.xpd.xpd.query.XpdQuery;
import com.yxt.talent.rv.controller.manage.xpd.xpd.viewobj.XpdVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.UTreeEnum;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.bizmgr.UserFocusMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserFocusPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdImportPO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.ubiz.tree.domain.entity.UTreeNode;
import com.yxt.ubiz.tree.service.UTreeNodeService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.common.util.StreamUtil.list2map;
import static com.yxt.common.util.StreamUtil.mapList;
import static com.yxt.talent.rv.application.prj.prj.legacy.PrjAppService.ROLE_CODE_TALENT_REVIEW_MANAGE;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TRANSFERABLE_RESOURCES_CODE_PRJ;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class XpdAppService {

    private final XpdMapper xpdMapper;
    private final XpdDimMapper xpdDimMapper;
    private final UserFocusMapper userFocusMapper;
    private final UTreeNodeService uTreeNodeService;
    private final L10nAclService l10nAclService;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final SpsdAclService spsdAclService;
    private final XpdImportMapper xpdImportMapper;

    public PagingList<DeptPrjClientVO> getMyDeptProjects(
        String orgId, String userId, DeptProjectClientQuery criteria) {
        IPage<DeptPrjClientVO> page = new Page<>(criteria.getCurPage(), criteria.getLimit());
        IPage<DeptPrjClientVO> result = xpdMapper.selectMyDeptProjects(page, orgId, userId, criteria);

        // fill categoryName
        fillCategoryName(orgId, result.getRecords());

        // fill completedUserCnt
        fillCompletedUserCnt(orgId, criteria, result.getRecords());

        // fill isFocused
        fillFocused(orgId, userId, result.getRecords());

        return BeanCopierUtil.toPagingList(result);
    }

    private void fillFocused(String orgId, String userId, List<DeptPrjClientVO> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }

        List<String> prjIds = datas.stream().map(DeptPrjClientVO::getId).collect(Collectors.toList());
        List<UserFocusPO> userFocusList = userFocusMapper.selectByOrgIdAndUserIdAndTargetIds(orgId, userId, prjIds);
        if (CollectionUtils.isEmpty(userFocusList)) {
            return;
        }

        Map<String, UserFocusPO> prjIdUserFocusMap = list2map(userFocusList, UserFocusPO::getTargetId);
        datas.forEach(data -> {
            UserFocusPO userFocus = prjIdUserFocusMap.get(data.getId());
            data.setFocused(userFocus != null);
        });
    }

    public void fillCompletedUserCnt(
        String orgId, DeptProjectClientQuery criteria, List<DeptPrjClientVO> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }
        List<String> projectIds = mapList(datas, DeptPrjClientVO::getId);
        // 计算已盘点人数
        List<PrjCompletedUserDTO> prjCompletedUserDtos =
            xpdMapper.selectProjectCompletedUsers(orgId, projectIds, criteria);
        // 按照项目id分组，计算完成人数
        Map<String, Integer> projectIdCntMap = prjCompletedUserDtos.stream()
            .collect(Collectors.toMap(PrjCompletedUserDTO::getProjectId, PrjCompletedUserDTO::getUserCnt));
        datas.forEach(data -> {
            Integer completedUserCnt = projectIdCntMap.get(data.getId());
            data.setCompletedUserCnt(completedUserCnt == null ? 0 : completedUserCnt);
        });
    }

    public void fillCategoryName(String orgId, List<DeptPrjClientVO> datas) {
        List<String> cateIds = mapList(datas, DeptPrjClientVO::getProjectCategoryId);
        List<UTreeNode> simpleNodes = uTreeNodeService.getSimpleNodeIds(orgId, UTreeEnum.XPD_BASE.getTreeId(), cateIds);
        Map<String, UTreeNode> idCategoryMap = list2map(simpleNodes, UTreeNode::getNid);
        datas.forEach(data -> {
            UTreeNode category = idCategoryMap.get(data.getProjectCategoryId());
            if (category != null) {
                data.setProjectCategoryName(category.getNodeName());
            }
        });
    }

    public DeptXpdStatisticsClientVO getMyDeptProjectStatistics(
        String orgId, String userId, DeptProjectClientQuery criteria, String lang) {
        // xpd参与盘点总人数 和 完成人数
        XpdUserCntDTO xpdUserCnt = xpdResultUserMapper.selectActvUserCnt(orgId, userId, criteria);
        // 统计各人才层级的人数
        List<PrjOverviewResultDTO> userResults = xpdResultUserMapper.selectDeptResultGroupByLevel(orgId, userId, criteria);
        DeptXpdStatisticsClientVO result = new DeptXpdStatisticsClientVO();
        result.setTotalUserCnt(xpdUserCnt.getTotalUserCnt() == null ? 0 : xpdUserCnt.getTotalUserCnt());
        result.setCompletedUserCnt(xpdUserCnt.getCompletedUserCnt() == null ? 0 : xpdUserCnt.getCompletedUserCnt());
        result.setUserResults(userResults);
        result.setProjectName(xpdUserCnt.getProjectName());
        result.setProjectStatus(xpdUserCnt.getProjectStatus());
        result.setStartTime(xpdUserCnt.getStartTime());
        result.setEndTime(xpdUserCnt.getEndTime());
        result.setId(xpdUserCnt.getId());
        return result;
    }


    public PagingList<DeptPrjUserClientVO> getMyDeptProjectUser(
        String orgId, String userId, DeptProjectClientQuery criteria, String lang) {
        IPage<DeptPrjUserClientVO> page = new Page<>(criteria.getCurPage(), criteria.getLimit());

        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        Set<String> l10nUserIds =
            l10nAclService.searchContentByKey(enableLocalization, List.of(orgId),
                ResourceTypeEnum.USER, criteria.getSearchKey());
        if (CollectionUtils.isNotEmpty(l10nUserIds)) {
            log.debug("LOG20353:");
            criteria.setUserIds(new ArrayList<>(l10nUserIds));
        }

        IPage<DeptPrjUserClientVO> result = xpdResultUserMapper.selectMyDeptXpdtUser(page, orgId, userId, criteria);

        l10nAclService.translateList(enableLocalization, List.of(orgId), lang,
            DeptPrjUserClientVO.class, result.getRecords());
        // fillPrjResult 处理盘点结果
        fillUserDimResult(result.getRecords(), orgId, criteria);
        return BeanCopierUtil.toPagingList(result);
    }

    private void fillUserDimResult(
        List<DeptPrjUserClientVO> datas, String orgId, DeptProjectClientQuery criteria) {
        if (CollectionUtils.isEmpty(datas)) {
            return;
        }

        String xpdId = criteria.getPrjId();
        Validate.isNotBlank(xpdId, ExceptionKeys.XPD_ID_EMPTY);

        XpdResultQuery query = new XpdResultQuery();
        query.setScopeDeptIds(criteria.getScopeDeptIds());
        query.setScopeUserIds(criteria.getScopeUserIds());
        List<XpdTableResultVO> xpdUserDimResults = xpdResultUserDimMapper.selectUserDimResult(orgId, xpdId, query);

        // 填充维度名称，并进行国际化翻译
        spsdAclService.fillNamesAndI18nNames(xpdUserDimResults, orgId);

        xpdUserDimResults.stream().collect(Collectors.groupingBy(UserBaseInfo::getUserId, Collectors.toList()))
            .forEach((userId, userDimResults) -> {
                Optional<DeptPrjUserClientVO> user = datas.stream().filter(e -> e.getUserId().equals(userId)).findFirst();
                user.ifPresent(deptPrjUserClientVO -> deptPrjUserClientVO.setResults(buildDimResultMap(userDimResults)));
            });
    }

    private Map<String, String> buildDimResultMap(List<XpdTableResultVO> userDimResults) {
        if (CollectionUtils.isEmpty(userDimResults)) {
            return Collections.emptyMap();
        }
        return userDimResults.stream()
            .filter(e -> isNotBlank(e.getSdDimName()) && isNotBlank(e.getLevelName()))
            .collect(Collectors.toMap(XpdTableResultVO::getSdDimName, XpdTableResultVO::getLevelName));
    }

    public List<XpdVO> search(String orgId, XpdQuery xpdQuery) {
        return xpdMapper.search(orgId, xpdQuery);
    }

    public CommonList<String> queryXpdNameByModelId(String orgId, List<String> modelIds) {
        List<String> xpdNames = xpdMapper.queryXpdNameByModelId(orgId, modelIds);
        return new CommonList<>(xpdNames);
    }

    /**
     * 检查项目是否添加了活动，包括在线评估和导入结果
     * @param orgId
     * @param xpdId
     * @return
     */
    public Boolean isActvExists(String orgId, String xpdId) {
        List<XpdImportPO> xpdImports = xpdImportMapper.selectByXpdIdAndOrgId(orgId, xpdId);
        List<ActivityArrangeItem> arrangeItems = xpdMapper.selectActvArrangeItemByXpdId(orgId, xpdId);
        return CollectionUtils.isNotEmpty(xpdImports) || CollectionUtils.isNotEmpty(arrangeItems);
    }

    /**
     * 维度或者项目规则里包含高级公式
     * @param orgId
     * @param xpdId
     * @return
     */
    public boolean hasFormula(String orgId, String xpdId) {
        return CommonUtils.existCount(xpdDimMapper.existFormulaRule(orgId, xpdId));
    }

    private final UserTransferComponent userTransferComponent;
    private final CoreAclService coreAclService;
    private final ActivityService activityService;

    /**
     * 进行资源转移
     *
     * @param orgId
     * @param fromUserId
     * @param toUserId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferResource(String orgId, String fromUserId, String toUserId) {
        int cnt = activityService.countOwnedActivity(UacdTypeEnum.PRJ_XPD.getRegId(), orgId, fromUserId);
        userTransferComponent.transferResource4Xpd(orgId, fromUserId, toUserId,
            cnt, () -> {
                activityService.changeActivityOwner(UacdTypeEnum.PRJ_XPD.getRegId(), orgId, fromUserId, toUserId, "udp_mq");
                    /*prjMapper.transferResource(orgId, fromUserId, toUserId);
                    prjMgrMapper.transferResourceStep1(orgId, fromUserId, toUserId);
                    prjMgrMapper.transferResourceStep2(orgId, fromUserId, toUserId);*/

                // 将负责人添加到业务角色中
                coreAclService.addRoleUserByCode(
                    CommonUtil.PRODUCT_CODE,
                    ROLE_CODE_TALENT_REVIEW_MANAGE, Set.of(toUserId), orgId,
                    "transfer_resource", "transfer_resource");
            }, TRANSFERABLE_RESOURCES_CODE_PRJ);
    }

}
