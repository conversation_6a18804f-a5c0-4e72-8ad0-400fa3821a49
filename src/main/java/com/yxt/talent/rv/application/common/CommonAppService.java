package com.yxt.talent.rv.application.common;

import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.talent.rv.application.user.dto.ScopeAuthDTO;
import com.yxt.talent.rv.controller.common.query.SearchKeyScopeAuthQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.service.auth.AuthorizationService;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.OrginitAclService;
import com.yxt.udpfacade.bean.enums.ResourceTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 所有领域通用的业务逻辑，比如国际化搜索，机构员工和部门名称加解密处理等
 * 原则上这个类可以被其他领域（包括其他领域的AppService服务）依赖，但是它不应该依赖其他领域
 */
@Service
@RequiredArgsConstructor
public class CommonAppService {

    private final AuthService authService;
    private final OrginitAclService orginitAclService;
    private final L10nAclService l10nAclService;
    private final AuthorizationService authorizationService;
    private final UdpDeptMapper udpDeptMapper;

    public void fillAuthInfo(String orgId, SearchKeyScopeAuthQuery query, String nacCode, String dataPermCode) {
        UserCacheDetail operator = authService.getUserCacheDetail();
        // 获取搜索匹配的用户
        List<String> searchMatchedUserIds = getSearchMatchedUsers(orgId, query.getSearchKey(), operator);
        query.setSearchUserIds(searchMatchedUserIds);

        // 检查前端是否已提供scopeUserIds和scopeDeptIds
        boolean hasScopeUserIds = CollectionUtils.isNotEmpty(query.getScopeUserIds());
        boolean hasScopeDeptIds = CollectionUtils.isNotEmpty(query.getScopeDeptIds());
        
        // 如果前端已提供所有范围参数，则无需查询权限
        if (hasScopeUserIds && hasScopeDeptIds) {
            // 仅处理部门的子部门
            query.setScopeDeptIds(udpDeptMapper.selectChildDeptIds(orgId, query.getScopeDeptIds()));
            return;
        }
        
        // 获取用户的权限范围内可见的用户id和部门(及下属部门)id
        ScopeAuthDTO permission = authorizationService.getUserDataPermAuth(operator, nacCode, dataPermCode);
        query.setAdmin(permission.isAdmin());

        // 处理用户范围
        if (!hasScopeUserIds && CollectionUtils.isNotEmpty(permission.getScopeUserIds())) {
            query.setScopeUserIds(permission.getScopeUserIds());
        }

        // 处理部门范围
        List<String> scopeDeptIds = query.getScopeDeptIds();
        if (!hasScopeDeptIds) {
            scopeDeptIds = permission.getScopeDeptIds();
        }
        if (CollectionUtils.isNotEmpty(scopeDeptIds)) {
            query.setScopeDeptIds(udpDeptMapper.selectChildDeptIds(orgId, scopeDeptIds));
        }
    }

    private List<String> getSearchMatchedUsers(String orgId, String searchKey, UserCacheDetail operator) {
        // 企业加密
        List<String> searchUserIds =
            orginitAclService.searchContactList(orgId, searchKey, ResourceTypeEnum.USER.getValue(),
                operator.getSourceCode());
        //国际化人名模糊搜索
        Set<String> l10nUserIds =
            l10nAclService.searchContentByKey(l10nAclService.isEnableLocalization(orgId),
                List.of(orgId), ResourceTypeEnum.USER, searchKey);
        searchUserIds.addAll(l10nUserIds);
        return searchUserIds;
    }

}
