package com.yxt.talent.rv.application.xpd.common.dto;

import java.util.function.Supplier;

public class OptionalDataLoader<T> {
    private boolean loaded;
    private T data;
    private Supplier<T> dataGetter;

    public OptionalDataLoader(Supplier<T> dataGetter) {
        this.dataGetter = dataGetter;
    }

    public T requireData() {
        if (!loaded) {
            data = dataGetter.get();
            loaded = true;
        }
        return data;
    }
}
