package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CaliDimResultItemDto {
    @Schema(description = "人才标准的维度id")
    private String sdDimId;
    private String sdDimName;
    @Schema(description = "人才标准的维度id")
    private String sdIndicatorId;
    private String itemName;
    @Schema(description = "盘点结果bean", hidden = true)
    private CaliDimResultItemValDto xpdValBean;
    @Schema(description = "校准结果bean", hidden = true)
    private CaliDimResultItemValDto caliValBean;
    private BigDecimal totalScore;

    @Schema(description = "盘点结果")
    private String xpdVal;
    @Schema(description = "校准结果")
    private String caliVal;
}
