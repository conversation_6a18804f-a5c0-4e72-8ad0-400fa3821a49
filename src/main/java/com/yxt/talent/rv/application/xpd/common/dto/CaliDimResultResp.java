package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CaliDimResultResp {
    /**
     * @see CaliResultTypeEnum
     */
    @Schema(description = "1:维度分层等级，2:维度分数，3:指标分数，4：达标率，5:是否达标")
    private int resultType;
    private List<XpdGridLevelBriefDto> gridLevelList;
    private List<CaliDimResultItemDto> items;
    private List<CaliDimCombResultDto> combList;
    private Map<String, CaliDimResultBean> dimResultMap;
    //盘点项目结果
    private String xpdLevelId;
    @Schema(description = "盘点项目结果")
    private String xpdLevelName;
    //校准项目结果
    private String caliLevelId;
    @Schema(description = "校准项目结果")
    private String caliLevelName;

    @Schema(description = "发展建议")
    private String suggestion;

    @Schema(description = "校准原因")
    private String reason;
}
