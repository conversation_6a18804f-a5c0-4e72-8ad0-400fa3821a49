package com.yxt.talent.rv.application.meet.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliMeetDimDTO {

    @Schema(description = "维度名称")
    private String dimensionName;

    @Schema(description = "维度id")
    private String dimId;

    @Schema(description = "原始维度level（1-3对应低至高）,0为默认值，表示无结果")
    private int initLevel;

    @Schema(description = "原始等级维度名称")
    private String initLevelName;

    @Schema(description = "校准后维度level（1-3对应低至高）,0为默认值，表示无结果")
    private int lastLevel;

    @Schema(description = "校准后维度等级名称")
    private String lastLevelName;

    private Integer dimensionType;
}
