package com.yxt.talent.rv.application.xpd.common.enums;

public enum CaliResultTypeEnum {
    TYPE_DIM_LEVEL(1, "维度分层等级"),
    TYPE_DIM_SCORE(2, "维度得分"),
    TYPE_INDICATOR_SCORE(3, "指标得分"),
    TYPE_QUALIFIED_PTG(4, "达标率"),
    TYPE_QUALIFIED(5, "是否达标"),
    ;
    private int type;
    private String name;

    CaliResultTypeEnum(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
