package com.yxt.talent.rv.application.calimeet;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.enums.DeleteEnum;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetBaseInfoDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetCreatDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetEditDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetPageQueryDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetPageResDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliMeetUserStatisticDTO;
import com.yxt.talent.rv.application.todo.CaliMeetSceneStrategy;
import com.yxt.talent.rv.application.todo.TodoSenderComponent;
import com.yxt.talent.rv.domain.meet.meet.Meet;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/4/29
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CaliMeetAppService {

    private final CalimeetMapper calimeetMapper;
    private final CaliMeetMsgSender caliMeetMsgSender;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final AppProperties appProperties;
    private final CoreAclService coreAclService;

    /**
     * 校准会列表
     */
    public PagingList<CaliMeetPageResDTO> pageCaliMeet(String orgId, CaliMeetPageQueryDTO queryDTO,
            PageRequest pageRequest, String searchKey, List<Integer> caliMeetStatusList) {
        Page<CalimeetPO> requestPage = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CalimeetPO> pageRes = calimeetMapper.pageQuery(requestPage, orgId, queryDTO.getXpdId(), searchKey,
                caliMeetStatusList);
        PagingList<CalimeetPO> poPagingList = BeanCopierUtil.toPagingList(pageRes);
        if (CollectionUtils.isEmpty(poPagingList.getDatas())) {
            return new PagingList<>(new ArrayList<>(), poPagingList.getPaging());
        }
        PagingList<CaliMeetPageResDTO> res = new PagingList<>();
        res.setPaging(poPagingList.getPaging());
        List<String> caliMeetIds = poPagingList.getDatas().stream().map(CalimeetPO::getId).collect(Collectors.toList());
        List<CalimeetParticipantsPO> participantsPOList = calimeetParticipantsMapper.selectByCaliMeetIds(orgId,
                caliMeetIds);
        Map<String, List<CalimeetParticipantsPO>> caliMeetIdGroup = participantsPOList.stream()
                .collect(Collectors.groupingBy(CalimeetParticipantsPO::getCalimeetId));
        List<CaliMeetPageResDTO> resData = new ArrayList<>();
        List<CaliMeetUserStatisticDTO> meetUserStaticticDTOList = calimeetUserMapper.getCountByMeetIds(orgId,
                caliMeetIds);
        Map<String, Integer> meetUserCOuntMap = meetUserStaticticDTOList.stream()
                .collect(Collectors.toMap(CaliMeetUserStatisticDTO::getMeetId, CaliMeetUserStatisticDTO::getUserCount));
        poPagingList.getDatas().forEach(item -> {
            CaliMeetPageResDTO data = new CaliMeetPageResDTO();
            data.setId(item.getId());
            data.setMeetName(item.getCalimeetName());
            data.setCaliType(item.getCalimeetType());
            data.setMeetStatus(item.getCalimeetStatus());
            data.setMtStartTime(item.getStartTime());
            data.setMtEndTime(item.getEndTime());
            data.setCaliMode(item.getCalimeetMode());
            data.setCreateTime(item.getCreateTime());
            data.setCreateUserId(item.getCreateUserId());
            data.setMeetRecordId(item.getRecord());
            data.setShowRateControl(item.getShowRatio());
            //设置校准人数
            data.setCaliUserCount(meetUserCOuntMap.getOrDefault(item.getId(), 0));
            List<CalimeetParticipantsPO> participants = caliMeetIdGroup.getOrDefault(item.getId(), new ArrayList<>());
            data.setOrganizeUserIds(
                    participants.stream().filter(el -> el.getUserType() == 1).map(CalimeetParticipantsPO::getUserId)
                            .collect(Collectors.toList()));
            data.setCaliUserIds(
                    participants.stream().filter(el -> el.getUserType() == 2).map(CalimeetParticipantsPO::getUserId)
                            .collect(Collectors.toList()));
            resData.add(data);
        });
        res.setDatas(resData);
        return res;
    }

    /**
     * 创建校准会
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String createCaliMeet(String orgId, String xpdId, CaliMeetCreatDTO caliMeetCreatDTO, String optUser) {
        CalimeetPO entity = new CalimeetPO();
        String caliMeetId = ApiUtil.getUuid();
        entity.setId(caliMeetId);
        entity.setCalimeetStatus(0);
        entity.setXpdId(xpdId);
        entity.setCalimeetName(caliMeetCreatDTO.getMeetName());
        entity.setCalimeetType(caliMeetCreatDTO.getMeetType());
        entity.setCalimeetMode(caliMeetCreatDTO.getMeetMode());
        //0-在线校准，1-线下校准
        if (caliMeetCreatDTO.getMeetMode() == 0) {
            entity.setEndTime(caliMeetCreatDTO.getMtEndTime());
        }
        entity.setStartTime(caliMeetCreatDTO.getMtStartTime());
        entity.setShowRatio(caliMeetCreatDTO.getShowRatio());
        entity.setOrgId(orgId);
        entity.setDeleted(0);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateUserId(optUser);
        entity.setUpdateUserId(optUser);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setRecord(caliMeetCreatDTO.getMeetRecordId());
        calimeetMapper.insert(entity);
        //保存校准人和组织人
        List<CalimeetParticipantsPO> participantsPOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(caliMeetCreatDTO.getOrganizeUserIds())) {
            caliMeetCreatDTO.getOrganizeUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 1, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(caliMeetCreatDTO.getCaliUserIds())) {
            caliMeetCreatDTO.getCaliUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 2, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            calimeetParticipantsMapper.batchInsert(participantsPOList);
        }
        return caliMeetId;
    }

    @NotNull
    private CalimeetParticipantsPO getCalimeetParticipantsPO(String caliMeetId, int userType, String userId,
            String orgId, String optUser) {
        CalimeetParticipantsPO participantsPO = new CalimeetParticipantsPO();
        participantsPO.setId(ApiUtil.getUuid());
        participantsPO.setCalimeetId(caliMeetId);
        //干系人类型(1-组织者，2-校准人)
        participantsPO.setUserType(userType);
        participantsPO.setUserId(userId);
        participantsPO.setOrgId(orgId);
        participantsPO.setCaliStatus(0);
        participantsPO.setCreateTime(LocalDateTime.now());
        participantsPO.setCreateUserId(optUser);
        participantsPO.setUpdateTime(LocalDateTime.now());
        participantsPO.setUpdateUserId(optUser);
        participantsPO.setDeleted(0);
        return participantsPO;
    }

    /**
     * 编辑校准会
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void editCaliMeet(String orgId, CaliMeetEditDTO caliMeetEditDTO, String optUser, String token,
            UserCacheDetail userCacheBasic) {
        String caliMeetId = caliMeetEditDTO.getId();
        if (StringUtils.isBlank(caliMeetId)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(orgId, caliMeetId);
        if (Objects.isNull(calimeetPO)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        if (!Integer.valueOf(0).equals(calimeetPO.getCalimeetStatus())) {
            throw new ApiException(ExceptionKeys.CALI_MEET_STATUS_NOT_EDIT);
        }
        calimeetPO.setCalimeetName(caliMeetEditDTO.getMeetName());
        calimeetPO.setCalimeetType(caliMeetEditDTO.getMeetType());
        calimeetPO.setCalimeetMode(caliMeetEditDTO.getMeetMode());
        //0-在线校准，1-线下校准
        if (caliMeetEditDTO.getMeetMode() == 0) {
            calimeetPO.setEndTime(caliMeetEditDTO.getMtEndTime());
        }
        calimeetPO.setStartTime(caliMeetEditDTO.getMtStartTime());
        calimeetPO.setShowRatio(caliMeetEditDTO.getShowRatio());
        calimeetPO.setUpdateUserId(optUser);
        calimeetPO.setUpdateTime(LocalDateTime.now());
        calimeetMapper.updateById(calimeetPO);
        List<CalimeetParticipantsPO> caliMeetParticipantsOld = calimeetParticipantsMapper.selectByCaliMeetId(orgId,
                caliMeetId);
        List<String> caliUserIdsOld = caliMeetParticipantsOld.stream()
                .filter(el -> Integer.valueOf(2).equals(el.getUserType())).map(CalimeetParticipantsPO::getUserId)
                .collect(Collectors.toList());
        //先删除关系人员
        calimeetParticipantsMapper.deleteByMeetId(orgId, caliMeetId);
        //保存校准人和组织人
        List<CalimeetParticipantsPO> participantsPOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getOrganizeUserIds())) {
            caliMeetEditDTO.getOrganizeUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 1, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.getCaliUserIds().forEach(item -> {
                CalimeetParticipantsPO participantsPO = getCalimeetParticipantsPO(caliMeetId, 2, item, orgId, optUser);
                participantsPOList.add(participantsPO);
            });
        }
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            calimeetParticipantsMapper.batchInsert(participantsPOList);
        }
        if (Objects.isNull(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.setCaliUserIds(new ArrayList<>());
        }
        //查询哪些校准人被移除发移除消息
        if (CollectionUtils.isNotEmpty(caliUserIdsOld)) {
            List<String> removeUserIds = caliUserIdsOld.stream()
                    .filter(e -> !caliMeetEditDTO.getCaliUserIds().contains(e)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(removeUserIds)) {
                CommonUtil.execAfterCommitIfHas(
                        () -> caliMeetMsgSender.sendTemplateMessagePointUserAsync(token, userCacheBasic,
                                List.of(calimeetPO), 5, removeUserIds));
            }
        }
        //查询哪些校准人新增，发加入消息
        if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
            caliMeetEditDTO.getCaliUserIds().removeAll(caliUserIdsOld);
            //发消息
            if (CollectionUtils.isNotEmpty(caliMeetEditDTO.getCaliUserIds())) {
                CommonUtil.execAfterCommitIfHas(
                        () -> caliMeetMsgSender.sendTemplateMessage(token, userCacheBasic, List.of(calimeetPO), 1));
            }
        }

    }


    /**
     * 校准会基本信息查询
     */
    @NotNull
    public CaliMeetBaseInfoDTO detailCaliMeet(String orgId, String caliMeetId) {
        if (StringUtils.isBlank(caliMeetId)) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        CalimeetPO calimeetPO = calimeetMapper.selectByIdAndOrgId(caliMeetId, orgId);
        Validate.isNotNull(calimeetPO, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        CaliMeetBaseInfoDTO res = new CaliMeetBaseInfoDTO();
        res.setMeetName(calimeetPO.getCalimeetName());
        res.setMeetMode(calimeetPO.getCalimeetMode());
        res.setMeetType(calimeetPO.getCalimeetType());
        res.setShowRatio(calimeetPO.getShowRatio());
        res.setMtStartTime(calimeetPO.getStartTime());
        res.setMtEndTime(calimeetPO.getEndTime());
        res.setId(calimeetPO.getId());
        res.setMeetRecordId(calimeetPO.getRecord());
        res.setMeetStatus(calimeetPO.getCalimeetStatus());
        List<CalimeetParticipantsPO> participantsPOList = calimeetParticipantsMapper.selectByCaliMeetId(orgId,
                caliMeetId);
        if (CollectionUtils.isNotEmpty(participantsPOList)) {
            List<CalimeetParticipantsPO> orgUsers = participantsPOList.stream().filter(el -> el.getUserType() == 1)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orgUsers)) {
                res.setOrganizeUserIds(orgUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()));
            }
            List<CalimeetParticipantsPO> caliUsers = participantsPOList.stream().filter(el -> el.getUserType() == 2)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(caliUsers)) {
                res.setCaliUserIds(caliUsers.stream().map(CalimeetParticipantsPO::getUserId).distinct()
                        .collect(Collectors.toList()));
            }
        }
        return res;
    }

    /**
     * 删除校准会
     *
     * @param id     校准会id
     * @param userId 用户id
     * @param orgId  组织id
     */
    public void deleteMeeting(String id, String userId, String orgId) {
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        meetEntity.setDeleted(DeleteEnum.DELETED.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);
    }


    /**
     * 结束校准会
     *
     * @param id     校准会id
     * @param userId 用户id
     * @param orgId  组织id
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void closeMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        meetEntity.setCalimeetStatus(Meet.CaliMeetStatusEnum.FINISHED.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 4));

        // 数据同步
        //syncCalibrationData(cmEntity, userId);
    }


    /**
     * 启动校准会
     *
     * @param id       校准会id
     * @param userId   用户id
     * @param orgId    组织id
     * @param operator 用户信息
     * @param token    token
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void openMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }
        meetEntity.setCalimeetStatus(Meet.CaliMeetStatusEnum.UNDERWAY.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 1));
        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 2));
        // 发送待办
        CommonUtil.execAfterCommitIfHas(() -> addCaliMeetTodo(orgId, operator.getUserId(), meetEntity, null));
    }

    /**
     * 撤回校准会
     *
     * @param id       校准会id
     * @param userId   用户id
     * @param orgId    组织id
     * @param operator 用户信息
     * @param token    token
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void withdrawMeeting(String id, String userId, String orgId, UserCacheDetail operator, String token) {
        // 验证
        int count = calimeetMapper.countByOrgIdAndId(orgId, id);
        if (count <= 0) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 更新状态
        CalimeetPO meetEntity = calimeetMapper.selectByIdAndOrgId(id, orgId);
        if (meetEntity == null) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        // 校验是否可以撤回
        if (meetEntity.getCalimeetStatus() != Meet.CaliMeetStatusEnum.UNDERWAY.getCode()) {
            throw new ApiException(ExceptionKeys.CALI_MEET_NOT_EXISTED);
        }

        meetEntity.setCalimeetStatus(Meet.CaliMeetStatusEnum.DEFAULT.getCode());
        EntityUtil.setUpdate(meetEntity, userId);
        calimeetMapper.updateById(meetEntity);

        // 发送消息
        CommonUtil.execAfterCommitIfHas(
                () -> caliMeetMsgSender.sendTemplateMessage(token, operator, List.of(meetEntity), 3));
        // 删除待办
        CommonUtil.execAfterCommitIfHas(() -> deleteTodoByUIds(orgId, operator.getUserId(), ListUtil.toList(id)));
    }


    /**
     * 添加校准会待办
     *
     * @param orgId         组织id
     * @param opUserId      操作人id
     * @param calimeet      校准会信息
     * @param caliMeetUsers 校准会参与者列表
     */
    private void addCaliMeetTodo(String orgId, String opUserId, CalimeetPO calimeet,
            List<CalimeetParticipantsPO> caliMeetUsers) {
        if (CollectionUtils.isNotEmpty(caliMeetUsers)) {
            // CaliMeetTodoInfoDto
            CaliMeetSceneStrategy.CaliMeetTodoInfoDto caliMeetTodoInfoDto = new CaliMeetSceneStrategy.CaliMeetTodoInfoDto();
            // 设置caliMeetUsers列表
            caliMeetTodoInfoDto.setCaliMeetUsers(caliMeetUsers);
            // 设置calimeet
            caliMeetTodoInfoDto.setMeetName(calimeet.getCalimeetName());
            caliMeetTodoInfoDto.setStartTime(calimeet.getStartTime());
            caliMeetTodoInfoDto.setEndTime(calimeet.getEndTime());

            String url = coreAclService.getScanentryURL(orgId, appProperties.getCaliMeetMsgUrl(), "");
            // 设置跳转url
            caliMeetTodoInfoDto.setUrl(url);

            try {
                TodoSenderComponent.getCaliMeetSceneStrategy().createTodos(orgId, opUserId, caliMeetTodoInfoDto);
            } catch (Exception e) {
                log.error("待办异常 addCaliMeetTodo error. first:{}", JSON.toJSONString(caliMeetUsers.get(0)), e);
            }
        }
    }

    /**
     * 删除校准会待办
     *
     * @param orgId    组织id
     * @param opUserId 操作人id
     * @param meetIds  校准会id列表
     */
    private void deleteTodoByUIds(String orgId, String opUserId, List<String> meetIds) {
        if (CollectionUtils.isNotEmpty(meetIds)) {
            // 调用deleteTodos方法
            try {
                TodoSenderComponent.getCaliMeetSceneStrategy().deleteTodos(orgId, opUserId,
                        meetIds.stream().map(String::valueOf).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("撤回校准会清空所有待办 deleteTodoByIds error. first:{}", JSON.toJSONString(meetIds.get(0)),
                        e);
            }
        }
    }

    /**
     * 删除盘点项目的校准会
     *
     * @param orgId      机构id
     * @param xpdId      项目id
     * @param operatorId 操作人
     * @return
     */
    public void deleteMeetingByXpdId(String orgId, String xpdId, String operatorId) {
        calimeetMapper.deleteMeetingByXpdId(orgId, xpdId, operatorId);
    }
}
