package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.infrastructure.common.transfer.impt.ImportContent;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class CaliUserImportDTO implements ImportContent{
    // 用户Id
    private String userId;
    // 用户姓名
    private String fullName;
    // 用户账号
    private String userName;
    //  维度 , key: 维度名称，value：维度结果
    private Map<String, String> dmResults = new LinkedHashMap<>();

    // 读取出来的原始数据，key为i，value为单元格数据
    private Map<String, String> originDmResults = new LinkedHashMap<>();

    // 指标模板的头部数据
    private Map<String, List<String>> headDatas = new LinkedHashMap<>();
    // 指标模板的用户数据
    private List<String> originIndicatorDatas = new LinkedList<>();

    private String reason;

    private String recommend;

    // 错误消息
    private String errorMsg;
}
