package com.yxt.talent.rv.application.activity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.yxt.aom.activity.service.rollup.impl.ResultRollUpService;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.aom.base.service.common.ActivityService;
import com.yxt.aom.datamodel.activityresult.AssessmentActivityResult;
import com.yxt.aom.datamodel.activityresult.IndexResult;
import com.yxt.aom.datamodel.common.ActionEnum;
import com.yxt.aom.datamodel.common.Actor;
import com.yxt.aom.datamodel.common.TargetObject;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BatchOperationUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.Validate;
import com.yxt.spmodel.facade.bean.rule.ExecuteRuleVO;
import com.yxt.spmodel.facade.bean.rule.LabelConditionJsonBean;
import com.yxt.spsdfacade.bean.spsd.IndicatorBaseDto;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdk.spmodel.SpmodelRuleMatchService;
import com.yxt.spsdk.spmodel.bean.LabelConditionResultBean;
import com.yxt.talent.rv.application.activity.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.UacdTypeEnum;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.CalcLogTypeEnum;
import com.yxt.talent.rv.domain.user.User;
import com.yxt.talent.rv.domain.user.UserDomainRepo;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileIndicatorPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfilePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultDetailPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityProfileResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdCalcLogPO;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileIndicatorRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileResultDetailRepo;
import com.yxt.talent.rv.infrastructure.repository.activity.ActivityProfileResultRepo;
import com.yxt.talent.rv.infrastructure.repository.aom.RvActivityParticipationMemberRepo;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdCalcBatchDTO;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdCalcLogRepo;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.remote.impl.RocketMqAclSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_PROFILE_ACTIVITY_CALC;

/**
 * 人才档案 - 创建
 * 人才档案 - 选择指标列表
 * 人才档案 - 编辑
 * 人才档案 - 详情
 * 人才档案 - 人员列表
 * 人才档案 - 人员导出
 * 人才档案 - 计算
 * 人才档案 - 人员评估结果
 *
 * <AUTHOR>
 * @since 2024/12/6
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ActivityProfileService {

    private final ActivityProfileRepo activityProfileRepo;
    private final ActivityProfileIndicatorRepo activityProfileIndicatorRepo;
    private final ActivityProfileResultRepo activityProfileResultRepo;
    private final ActivityProfileResultDetailRepo activityProfileResultDetailRepo;
    private final UserDomainRepo userDomainRepo;
    private final SpmodelRuleMatchService spmodelRuleMatchService;
    private final SpmodelAclService spmodelAclService;
    private final ActivityProfileTslManager activityProfileTslManager;
    private final ActivityProfileExportService activityProfileExportService;
    private final SpsdAclService spsdAclService;
    private final RvActivityParticipationMemberRepo rvActivityParticipationMemberRepo;
    private final XpdCalcLogRepo xpdCalcLogRepo;
    private final ILock lockService;
    private final AomActivityService aomActivityService;
    private final ActivityService activityService;
    private final ResultRollUpService resultRollUpService;
    private final RocketMqAclSender rocketMqAclSender;
    private final XpdService xpdService;
    private final ActivityCalcComponent activityCalcComponent;


    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String create(String orgId, ActProfileCreateDTO createDTO, String optUserId) {
        //创建基本信息
        ActivityProfilePO profilePO = new ActivityProfilePO();
        profilePO.setId(ApiUtil.getUuid());
        profilePO.setOrgId(orgId);
        if (StringUtils.isNotBlank(createDTO.getName()) && createDTO.getName().length() > 200) {
            createDTO.setName(createDTO.getName().substring(0, 200));
        } else {
            profilePO.setProfileName(createDTO.getName());
        }
        profilePO.setModelId(createDTO.getModelId());
        profilePO.setAomActvId(profilePO.getId());
        profilePO.setEvalTimeType(createDTO.getEvalTimeType());
        if (createDTO.getEvalTimeType() == 2 && Objects.nonNull(createDTO.getEvalTime())) {
            profilePO.setEvalTime(createDTO.getEvalTime());
        }
        profilePO.setScoreQualified(createDTO.getQualifiedScore());
        profilePO.setScoreUnqualified(createDTO.getUnqualifiedScore());
        profilePO.setActvDesc(createDTO.getDescription());
        profilePO.setDeleted(0);
        profilePO.setCreateTime(LocalDateTime.now());
        profilePO.setCreateUserId(optUserId);
        profilePO.setUpdateUserId(optUserId);
        profilePO.setUpdateTime(LocalDateTime.now());
        activityProfileRepo.insertEntity(profilePO);
        String actProfileId = profilePO.getId();
        //保存关联指标信息
        saveIndicatorInfo(orgId, createDTO.getIndicators(), optUserId, actProfileId);
        return actProfileId;
    }

    private LocalDateTime formatStringToLocalDateTime(String dateTimeStr) {
        // Custom DateTimeFormatter
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
        return LocalDateTime.parse(dateTimeStr, formatter);
    }

    private void saveIndicatorInfo(String orgId, List<ActProfileIndicatorDTO> indicators, String optUserId,
            String actProfileId) {
        if (CollectionUtils.isNotEmpty(indicators)) {
            List<ActivityProfileIndicatorPO> profileIndicatorPOList = new ArrayList<>();
            int orderIndex = 1;
            for (ActProfileIndicatorDTO indicatorItem : indicators) {
                ActivityProfileIndicatorPO profileIndicatorPO = new ActivityProfileIndicatorPO();
                profileIndicatorPO.setId(ApiUtil.getUuid());
                profileIndicatorPO.setActvProfileId(actProfileId);
                profileIndicatorPO.setOrgId(orgId);
                profileIndicatorPO.setOrderIndex(orderIndex);
                profileIndicatorPO.setSdIndicatorId(indicatorItem.getIndicatorId());
                profileIndicatorPO.setRuleId(Long.valueOf(indicatorItem.getIndicatorRuleId()));
                profileIndicatorPO.setDeleted(0);
                profileIndicatorPO.setCreateTime(LocalDateTime.now());
                profileIndicatorPO.setCreateUserId(optUserId);
                profileIndicatorPO.setUpdateUserId(optUserId);
                profileIndicatorPO.setUpdateTime(LocalDateTime.now());
                profileIndicatorPOList.add(profileIndicatorPO);
                orderIndex++;
            }
            activityProfileIndicatorRepo.batchInsert(profileIndicatorPOList);
        }
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void update(String orgId, ActProfileCreateDTO createDTO, String optUserId) {
        String actProfileId = createDTO.getId();
        if (StringUtils.isBlank(actProfileId)) {
            throw new ApiException(ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        }
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, createDTO.getId());
        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        if (StringUtils.isNotBlank(createDTO.getName()) && createDTO.getName().length() > 200) {
            createDTO.setName(createDTO.getName().substring(0, 200));
        } else {
            profilePO.setProfileName(createDTO.getName());
        }
        profilePO.setModelId(createDTO.getModelId());
        profilePO.setAomActvId(createDTO.getAomActId());
        profilePO.setEvalTimeType(createDTO.getEvalTimeType());
        if (createDTO.getEvalTimeType() == 2 && Objects.nonNull(createDTO.getEvalTime())) {
            profilePO.setEvalTime(createDTO.getEvalTime());
        }
        profilePO.setScoreQualified(createDTO.getQualifiedScore());
        profilePO.setScoreUnqualified(createDTO.getUnqualifiedScore());
        profilePO.setActvDesc(createDTO.getDescription());
        profilePO.setUpdateUserId(optUserId);
        profilePO.setUpdateTime(LocalDateTime.now());
        activityProfileRepo.updateEntity(profilePO);
        //更新指标，删除旧指标，重新添加
        activityProfileIndicatorRepo.deleteByActvId(orgId, actProfileId, optUserId);
        //保存关联指标信息
        saveIndicatorInfo(orgId, createDTO.getIndicators(), optUserId, actProfileId);
    }

    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void deleteById(String orgId, String actProfileId, String optUserId) {
        if (StringUtils.isBlank(actProfileId)) {
            //            throw new ApiException(ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
            return;
        }
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        if (Objects.isNull(profilePO)) {
            return;
        }
        //        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        activityProfileRepo.deleteById(orgId, actProfileId, optUserId);
        //删除关联指标
        activityProfileIndicatorRepo.deleteByActvId(orgId, actProfileId, optUserId);
    }

    /**
     * 查询人才档案详情信息
     *
     * @param orgId
     * @param actProfileId
     * @return
     */
    public ActProfileDetailVO detailById(String orgId, String actProfileId) {
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        ActProfileDetailVO vo = new ActProfileDetailVO();
        vo.setId(profilePO.getId());
        vo.setName(profilePO.getProfileName());
        vo.setEvalTimeType(profilePO.getEvalTimeType());
        vo.setEvalTime(profilePO.getEvalTime());
        vo.setQualifiedScore(profilePO.getScoreQualified());
        vo.setUnqualifiedScore(profilePO.getScoreUnqualified());
        vo.setDescription(profilePO.getActvDesc());
        vo.setModelId(profilePO.getModelId());
        List<ActProfileIndicatorVO> indicators = new ArrayList<>();
        //查询指标列表
        List<ActivityProfileIndicatorPO> profileIndicators = activityProfileIndicatorRepo.findByActProfileId(orgId,
                actProfileId);
        if (CollectionUtils.isNotEmpty(profileIndicators)) {
            String modelId = profilePO.getModelId();
            List<IndicatorDto> indicatorDtos = spsdAclService.getIndicatorByModelId(orgId, modelId);
            Map<String, String> indicatorIdNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(indicatorDtos)) {
                indicatorIdNameMap = indicatorDtos.stream().collect(Collectors.toMap(IndicatorDto::getItemId, item -> {
                    if (StringUtils.isBlank(item.getIndicatorName())) {
                        return item.getItemValue();
                    }
                    return item.getIndicatorName();
                }));
            }
            for (ActivityProfileIndicatorPO indicator : profileIndicators) {
                ActProfileIndicatorVO indicatorVo = new ActProfileIndicatorVO();
                indicatorVo.setId(indicator.getId());
                indicatorVo.setActvProfileId(actProfileId);
                indicatorVo.setSdIndicatorId(indicator.getSdIndicatorId());
                indicatorVo.setSdIndicatorName(indicatorIdNameMap.getOrDefault(indicator.getSdIndicatorId(), ""));
                indicatorVo.setRuleId(indicator.getRuleId());
                indicatorVo.setOrderIndex(indicator.getOrderIndex());
                indicators.add(indicatorVo);
            }
            indicators.sort(Comparator.comparingInt(ActProfileIndicatorVO::getOrderIndex));
            vo.setIndicators(indicators);
        }
        return vo;
    }

    /**
     * 获取活动信息
     *
     * @param orgId
     * @param actProfileId
     * @return
     */
    public ProfileStatisticsVO getProfileStatisticsInfo(String orgId, String actProfileId) {
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        //查询项目
        Activity aomActivity = activityService.findById(orgId, profilePO.getAomActvId());
        Validate.isNotNull(aomActivity, ExceptionKeys.ACTIVITY_NOT_EXIST);
        ProfileStatisticsVO vo = new ProfileStatisticsVO();
        vo.setEvalTimeType(profilePO.getEvalTimeType());
        if (profilePO.getEvalTimeType() == 2) {
            vo.setEvalTime(profilePO.getEvalTime());
        } else {
            //获取最近的计算时间
            XpdCalcLogPO xpdCalcLogPO = xpdCalcLogRepo.findLatestByProfId(orgId, CalcLogTypeEnum.ACT_PROFILE.getCode(),
                    actProfileId);
            if (xpdCalcLogPO != null) {
                vo.setEvalTime(xpdCalcLogPO.getStartTime());
            }
        }
        List<ActivityProfileIndicatorPO> profileIndicatorPOList = activityProfileIndicatorRepo.findByActProfileId(orgId,
                actProfileId);
        vo.setFinishedUserCount(0L);
        List<String> actUserIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId,
                aomActivity.getSourceId());
        if (CollectionUtils.isNotEmpty(profileIndicatorPOList)) {
            List<String> indicatorIds = profileIndicatorPOList.stream()
                    .map(ActivityProfileIndicatorPO::getSdIndicatorId).toList();
            String modelId = profilePO.getModelId();
            List<IndicatorDto> indicatorDtos = spsdAclService.getIndicatorByModelId(orgId, modelId);
            Map<String, String> indicatorIdNameMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(indicatorDtos)) {
                indicatorIdNameMap = indicatorDtos.stream().collect(Collectors.toMap(IndicatorDto::getItemId, item -> {
                    if (StringUtils.isBlank(item.getIndicatorName())) {
                        return item.getItemValue();
                    }
                    return item.getIndicatorName();
                }));
            }
            Map<String, String> finalIndicatorIdNameMap = indicatorIdNameMap;
            List<IndicatorIdNameDTO> indicatorStrList = new ArrayList<>();
            indicatorIds.forEach(indicatorId -> {
                if (finalIndicatorIdNameMap.containsKey(indicatorId)) {
                    IndicatorIdNameDTO indicatorIdNameDTO = new IndicatorIdNameDTO();
                    indicatorIdNameDTO.setId(indicatorId);
                    indicatorIdNameDTO.setName(finalIndicatorIdNameMap.get(indicatorId));
                    indicatorStrList.add(indicatorIdNameDTO);
                }
            });
            vo.setIndicatorStrList(indicatorStrList);
            //查询rv_activity_profile_result 用户人才档案匹配活动结果表
            if (CollectionUtils.isEmpty(actUserIds)) {
                vo.setFinishedUserCount(0L);
            } else {
                long finishedUserCount = activityProfileResultRepo.findFinishedUserCountRangeUser(orgId, actProfileId,
                        actUserIds);
                vo.setFinishedUserCount(finishedUserCount);
            }

        }
        //查询总人数rv_activity_participation_member
        Long partId = xpdService.getPartIdByActvId(orgId, actProfileId);
        long totalUserCount = rvActivityParticipationMemberRepo.findTotalUserCount(orgId, aomActivity.getSourceId(),
                partId);
        vo.setTotalUserCount(totalUserCount);
        return vo;
    }

    /**
     * 人员跟踪页面列表 -指定人员指标结果详情
     *
     * @param orgId
     * @param actProfileId
     * @param userId
     * @return
     */
    public List<ProfileUserMatchResultDetailVO> getUserMatchResultInfo(String orgId, String actProfileId,
            String userId) {
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        List<ProfileUserMatchResultDetailVO> detailResList = new ArrayList<>();

        // 查询rv_activity_profile_result 用户人才档案匹配活动结果表
        List<ActivityProfileResultPO> profileResultList = activityProfileResultRepo.findByActProfileIdAndUserIds(orgId,
                actProfileId, Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(profileResultList)) {
            return detailResList;
        }

        // 结果表主键ID，用于查询明细表关联查询
        List<String> userResultIds = profileResultList.stream().map(ActivityProfileResultPO::getId)
                .collect(Collectors.toList());

        // 查询活动模型-动态人才评估-结果明细表
        List<ActivityProfileResultDetailPO> resultDetailPOList = activityProfileResultDetailRepo.findByActProfileIdAndResultIds(
                orgId, userResultIds, actProfileId);
        Map<String, List<ActivityProfileResultDetailPO>> resultDetailMap = resultDetailPOList.stream()
                .collect(Collectors.groupingBy(ActivityProfileResultDetailPO::getUserResultId));

        String modelId = profilePO.getModelId();
        List<IndicatorDto> indicatorDtos = spsdAclService.getIndicatorByModelId(orgId, modelId);

        // 使用 Collectors.toMap 处理重复键的情况
        Map<String, String> indicatorIdNameMap = indicatorDtos.stream().collect(
                Collectors.toMap(IndicatorDto::getItemId, item -> StringUtils.isBlank(item.getIndicatorName()) ?
                        item.getItemValue() :
                        item.getIndicatorName(), (existing, replacement) -> existing // 或者选择 replacement，取决于业务逻辑
                ));

        profileResultList.forEach(profileResult -> {
            ProfileUserMatchResultDetailVO detailRes = new ProfileUserMatchResultDetailVO();
            detailRes.setIndicatorId(profileResult.getSdIndicatorId());
            detailRes.setIndicatorName(indicatorIdNameMap.getOrDefault(profileResult.getSdIndicatorId(), ""));
            detailRes.setQualified(profileResult.getQualified());
            List<ProfileUserIndicatorVO> indicatorDetailList = new ArrayList<>();
            List<ActivityProfileResultDetailPO> indicatorItemList = resultDetailMap.getOrDefault(profileResult.getId(),
                    new ArrayList<>());
            // 设置详细的指标/标签数据
            if (CollectionUtils.isNotEmpty(indicatorItemList)) {
                indicatorItemList.forEach(item -> {
                    ProfileUserIndicatorVO indicatorDetail = new ProfileUserIndicatorVO();
                    indicatorDetail.setId(item.getId());
                    indicatorDetail.setConditionId(item.getConditionId());
                    indicatorDetail.setLabelType(item.getLabelType());
                    indicatorDetail.setLabelId(item.getLabelId());
                    indicatorDetail.setQualified(item.getQualified());
                    indicatorDetail.setLabelValue(item.getLabelValue());
                    indicatorDetail.setLabelValueId(item.getLabelValueId());
                    indicatorDetailList.add(indicatorDetail);
                });
            }
            detailRes.setIndicatorDetailList(indicatorDetailList);
            detailResList.add(detailRes);
        });
        return detailResList;
    }

    /**
     * 人员跟踪页面列表
     *
     * @param pageRequest
     * @param orgId
     * @param actProfileId
     * @param param
     * @return
     */
    public PagingList<ActMemberUser> pageQuery(PageRequest pageRequest, String orgId, String actProfileId,
            ProfUserListParam param) {
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO activityProfilePO = activityProfileRepo.findById(orgId, actProfileId);
        Validate.isNotNull(activityProfilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
        searchParam.setKeyword(param.getKeyword());
        searchParam.setKwType(param.getKwType());
        searchParam.setDeptIds(param.getDeptIds());
        searchParam.setActvId(activityProfilePO.getId());
        searchParam.setPositionIds(param.getPositionIds());
        searchParam.setStatus(param.getStatus());
        Page<ActMemberUser> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        PagingList<ActMemberUser> userInfos = aomActivityService.getActivityUserList(page, orgId, searchParam);
        if (CollectionUtils.isEmpty(userInfos.getDatas())) {
            return new PagingList<>(new ArrayList<>(), userInfos.getPaging());
        }
        List<String> userIds = userInfos.getDatas().stream().map(ActMemberUser::getUserId).collect(Collectors.toList());
        List<ProfilePageUserVO> profUserResultList = activityProfileTslManager.getProfileUserInfo(orgId, actProfileId,
                userIds);
        //完成状态  0未开始,1进行中,2已完成
        Map<String, Integer> userFinishStatusMap = profUserResultList.stream()
                .collect(Collectors.toMap(ProfilePageUserVO::getUserId, ProfilePageUserVO::getFinishedStatus));
        //设置用户完成状态 完成结果，0未开始,1进行中,2已完成
        userInfos.getDatas()
                .forEach(userInfo -> userInfo.setResultStatus(userFinishStatusMap.get(userInfo.getUserId())));
        return userInfos;
    }

    /**
     * 异步形式通知计算。由于计算过程可能耗时大，采用异步形式通知计算。计算完成后，会将结果更新到数据库中。
     *
     * @param orgId
     * @param actProfileId
     * @param optUserId
     */
    public void sendCalcMq(String orgId, String actProfileId, String optUserId) {
        log.info("profileActCalc sendCalcMq 参数 orgId={},actProfileId={} optUserId={}", orgId, actProfileId,
                optUserId);
        ProfileActivityCalcMqDTO profileActivityCalcMqDTO = new ProfileActivityCalcMqDTO();
        profileActivityCalcMqDTO.setActProfId(actProfileId);
        profileActivityCalcMqDTO.setOrgId(orgId);
        profileActivityCalcMqDTO.setOptUserId(optUserId);
        rocketMqAclSender.send(TOPIC_PROFILE_ACTIVITY_CALC, JSON.toJSONString(profileActivityCalcMqDTO));
    }

    public void startCalculate(String orgId, String actProfileId, String optUserId) {
        log.info("startCalculate 参数 orgId={},actProfileId={},optUserId={} ", orgId, actProfileId, optUserId);
        if (StringUtils.isBlank(actProfileId) || StringUtils.isBlank(orgId)) {
            throw new ApiException(ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        }
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        if (Objects.isNull(profilePO)) {
            throw new ApiException(ExceptionKeys.PROFILE_NOT_FOUND);
        }
        //查询项目关联的指标
        List<ActivityProfileIndicatorPO> profileIndicatorPOList = activityProfileIndicatorRepo.findByActProfileId(orgId,
                actProfileId);
        if (CollectionUtils.isEmpty(profileIndicatorPOList)) {
            throw new ApiException(ExceptionKeys.PROFILE_INDICATOR_NOT_FOUND);
        }
        //接口请求加锁。防止并发计算同一活动，加锁时间5分钟
        String calCacheKey = String.format(RedisKeys.CACHE_ACT_PROFILE_CAL_INTERFACE, orgId, actProfileId);
        if (!lockService.tryLock(calCacheKey, 1, TimeUnit.MINUTES)) {
            throw new ApiException(ExceptionKeys.PROFILE_INTERFACE_CALCULATE_LOCK);
        }
        //发生MQ消息，异步计算匹配结果
        sendCalcMq(orgId, actProfileId, optUserId);
    }


    public void computeUserMatchResult(String orgId, String actProfileId, String optUserId) {
        log.info("computeUserMatchResult 参数={}", "actProfileId=" + actProfileId + ",orgId=" + orgId);
        if (StringUtils.isBlank(actProfileId) || StringUtils.isBlank(orgId)) {
            log.warn("LOG20443:computeUserMatchResult 参数异常={}", "actProfileId or orgId is null.");
            return;
        }
        //查询活动指标数据
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        if (Objects.isNull(profilePO)) {
            log.warn("LOG20453:computeUserMatchResult ActivityProfilePO 参数异常={}", "activity profile not found.");
            return;
        }
        //查询项目关联的指标
        List<ActivityProfileIndicatorPO> profileIndicatorPOList = activityProfileIndicatorRepo.findByActProfileId(orgId,
                actProfileId);
        if (CollectionUtils.isEmpty(profileIndicatorPOList)) {
            log.warn("LOG20463:computeUserMatchResult ActivityProfileIndicator 参数异常={}",
                    "activity profile indicator not found.");
            return;
        }
        Activity activity = activityService.findById(orgId, profilePO.getAomActvId());
        if (activity == null) {
            log.error("LOG20473:推送活动指标匹配结果异常actProfileId={},err=找不到对应的活动",
                    profilePO.getAomActvId());
            return;
        }
        //查询项目的状态，判断进行中才可以计算,查询当前项目的人员数据
        List<String> userIds = rvActivityParticipationMemberRepo.findAllUserIdByActId(orgId, activity.getSourceId());
        if (CollectionUtils.isEmpty(userIds)) {
            log.debug("LOG20483:");
            return;
        }
        doComputeUserMatchResult(orgId, actProfileId, optUserId, userIds, profilePO, profileIndicatorPOList, activity);
    }

    private void doComputeUserMatchResult(String orgId, String actProfileId, String optUserId, List<String> userIds,
            ActivityProfilePO profilePO, List<ActivityProfileIndicatorPO> profileIndicatorPOList, Activity activity) {
        String calCacheKey = String.format(RedisKeys.CACHE_ACT_PROFILE_CAL, orgId, actProfileId);
        //防止并发计算同一活动
        if (lockService.tryLock(calCacheKey, 10, TimeUnit.MINUTES)) {
            log.debug("LOG20493:{}", actProfileId);
            activityCalcComponent.startCalculate(orgId, actProfileId, CalcLogTypeEnum.ACT_PROFILE.getCode());

            //需要保存结果的数据
            List<ActivityProfileResultPO> profileResultPOList = new ArrayList<>();
            List<ActivityProfileResultDetailPO> profileResultDetailPOList = new ArrayList<>();
            //查询表中的所有人员之前计算的结果和明细数据，做后续删除插入操作
            List<ActivityProfileResultPO> oldProfileUserResultList = activityProfileResultRepo.findByActProfileIdAndUserIds(
                    orgId, actProfileId, userIds);
            List<String> oldUserResultIds = oldProfileUserResultList.stream().map(ActivityProfileResultPO::getId)
                    .collect(Collectors.toList());
            List<ActivityProfileResultDetailPO> oldProfileResultDetailPOList = activityProfileResultDetailRepo.findByActProfileIdAndResultIds(
                    orgId, oldUserResultIds, actProfileId);
            //记录开始计算
            XpdCalcBatchDTO calcBatch = xpdCalcLogRepo.newCalcBatch(orgId, CalcLogTypeEnum.ACT_PROFILE.getCode(),
                    actProfileId);
            //计算并保存人员的匹配结果
            try {
                BigDecimal totalScore = getTotalScore(orgId, profilePO.getId());
                Map<Integer, BigDecimal> qualifiedScoreConfigMap = new HashMap<>();
                qualifiedScoreConfigMap.put(1, profilePO.getScoreQualified());
                qualifiedScoreConfigMap.put(0, profilePO.getScoreUnqualified());
                List<String> indicatorIds = profileIndicatorPOList.stream()
                        .map(ActivityProfileIndicatorPO::getSdIndicatorId).collect(Collectors.toList());
                Map<String, Integer> indicatorIdTypeMap = spsdAclService.getIndicatorBaseInfoByIndicatorIds(orgId,
                                indicatorIds).stream()
                        .collect(Collectors.toMap(IndicatorBaseDto::getId, IndicatorBaseDto::getItemType));
                profileIndicatorPOList.forEach(indicator -> {
                    if (StringUtils.isBlank(indicator.getSdIndicatorId()) || Objects.isNull(indicator.getRuleId())) {
                        return;
                    }
                    String indicatorId = indicator.getSdIndicatorId();
                    ExecuteRuleVO executeRuleVO = spmodelAclService.getRule(indicator.getRuleId(), orgId,
                            AppConstants.SPMODEL_RULE_APP_CODE);
                    if (Objects.isNull(executeRuleVO)) {
                        log.warn("computeUserMatchResult executeRuleVO异常={}", "execute rule is null.");
                        return;
                    }
                    LabelConditionJsonBean labelConditionJsonBean = executeRuleVO.getRuleConfig();
                    if (Objects.isNull(labelConditionJsonBean)) {
                        log.warn("computeUserMatchResult labelConditionJsonBean异常={}", "rule config is null.");
                        return;
                    }
                    partHandleIndicatorResult(profilePO, optUserId, userIds, profileResultPOList,
                            profileResultDetailPOList, indicatorId, labelConditionJsonBean);
                });
                //删除旧数据保存新计算数据，事务使用
                activityProfileTslManager.delInsertNewData(orgId, profileResultPOList, profileResultDetailPOList,
                        oldUserResultIds, oldProfileResultDetailPOList);
                xpdCalcLogRepo.endCalcLog(calcBatch.getId(), true);
                //推送结果给amo,根据完成所有指标的人员数据
                profileResultPOList.forEach(profileResultPO -> pushProfUserRes2Amo(orgId, profilePO, totalScore,
                        profileResultPO.getUserId(), qualifiedScoreConfigMap, indicatorIdTypeMap, activity,
                        profileIndicatorPOList));
            } catch (Exception e) {
                log.error("LOG20503:计算活动指标匹配结果异常actProfileId={},err=", actProfileId, e);
                xpdCalcLogRepo.endCalcLog(calcBatch.getId(), false);
            } finally {
                lockService.unLock(calCacheKey);
                log.debug("LOG20513:{}", actProfileId);
                activityCalcComponent.completeCalculate(orgId, actProfileId, CalcLogTypeEnum.ACT_PROFILE.getCode());
            }
        }
    }

    /**
     * 单指标计算匹配结果，并保存数据
     *
     * @param profilePO
     * @param optUserId
     * @param userIds
     * @param profileResultPOList
     * @param profileResultDetailPOList
     * @param indicatorId
     * @param labelConditionJsonBean
     */
    private void partHandleIndicatorResult(ActivityProfilePO profilePO, String optUserId, List<String> userIds,
            List<ActivityProfileResultPO> profileResultPOList,
            List<ActivityProfileResultDetailPO> profileResultDetailPOList, String indicatorId,
            LabelConditionJsonBean labelConditionJsonBean) {

        //消费是一个个的获取
        BatchOperationUtil.batchExecute(userIds, 100,
                subItem -> spmodelRuleMatchService.calcRuleMatch(profilePO.getOrgId(), labelConditionJsonBean, subItem,
                        Function.identity(), (userId, conditionResultBean) -> {
                            log.info("计算活动指标匹配结果actId={},userId={},indicatorId={},res={}", profilePO.getId(),
                                    userId, indicatorId, JSON.toJSONString(conditionResultBean));
                            saveItemUserCalcResult(profilePO, optUserId, profileResultPOList, profileResultDetailPOList,
                                    indicatorId, userId, conditionResultBean);
                        }));
    }

    /**
     * 组装保存数据
     *
     * @param profilePO
     * @param optUserId
     * @param profileResultPOList
     * @param profileResultDetailPOList
     * @param indicatorId
     * @param userId
     * @param conditionResultBean
     */
    private void saveItemUserCalcResult(ActivityProfilePO profilePO, String optUserId,
            List<ActivityProfileResultPO> profileResultPOList,
            List<ActivityProfileResultDetailPO> profileResultDetailPOList, String indicatorId, String userId,
            LabelConditionResultBean conditionResultBean) {
        //保存匹配结果
        ActivityProfileResultPO result = new ActivityProfileResultPO();
        result.setId(ApiUtil.getUuid());
        result.setOrgId(profilePO.getOrgId());
        result.setActvProfileId(profilePO.getId());
        result.setUserId(userId);
        result.setSdIndicatorId(indicatorId);
        result.setQualified(Boolean.TRUE.equals(conditionResultBean.getMatched()) ? 1 : 0);
        result.setCreateUserId(optUserId);
        result.setCreateTime(LocalDateTime.now());
        result.setUpdateTime(LocalDateTime.now());
        result.setUpdateUserId(optUserId);
        profileResultPOList.add(result);
        LabelConditionResultBean.LabelRuleResult[] labelRuleResultArr = conditionResultBean.getRuleResults();
        //设置明细数据
        List<ActivityProfileResultDetailPO> subIndicatorResultList = new ArrayList<>();
        for (LabelConditionResultBean.LabelRuleResult labelRuleResultItem : labelRuleResultArr) {
            ActivityProfileResultDetailPO activityProfileResultDetailPO = new ActivityProfileResultDetailPO();
            activityProfileResultDetailPO.setId(ApiUtil.getUuid());
            activityProfileResultDetailPO.setOrgId(profilePO.getOrgId());
            activityProfileResultDetailPO.setActvProfileId(profilePO.getId());
            activityProfileResultDetailPO.setUserResultId(result.getId());
            activityProfileResultDetailPO.setUserId(userId);
            activityProfileResultDetailPO.setConditionId(labelRuleResultItem.getRuleInfo().getUuid());
            activityProfileResultDetailPO.setLabelType(labelRuleResultItem.getRuleInfo().getType());
            activityProfileResultDetailPO.setLabelId(String.valueOf(labelRuleResultItem.getRuleInfo().getId()));
            activityProfileResultDetailPO.setLabelValue(
                    CollectionUtils.isNotEmpty(labelRuleResultItem.getUserValues()) ?
                            String.join(",", labelRuleResultItem.getUserValues()) :
                            "");
            activityProfileResultDetailPO.setQualified(
                    Boolean.TRUE.equals(labelRuleResultItem.getUserMatchRule()) ? 1 : 0);
            activityProfileResultDetailPO.setCreateUserId(optUserId);
            activityProfileResultDetailPO.setCreateTime(LocalDateTime.now());
            activityProfileResultDetailPO.setUpdateTime(LocalDateTime.now());
            activityProfileResultDetailPO.setUpdateUserId(optUserId);
            subIndicatorResultList.add(activityProfileResultDetailPO);
        }
        profileResultDetailPOList.addAll(subIndicatorResultList);
    }

    private void pushProfUserRes2Amo(String orgId, ActivityProfilePO profilePO, BigDecimal totalScore, String userId,
            Map<Integer, BigDecimal> qualifiedScoreConfigMap, Map<String, Integer> indicatorIdTypeMap,
            Activity activity, List<ActivityProfileIndicatorPO> profileIndicatorPOList) {
        List<String> indicatorIds = profileIndicatorPOList.stream().map(ActivityProfileIndicatorPO::getSdIndicatorId)
                .toList();
        //查询当前用户的指标匹配结果
        List<ActivityProfileResultPO> activityProfileResultPOS = activityProfileResultRepo.findByActProfileIdAndUserIds(
                orgId, profilePO.getId(), Collections.singletonList(userId));
        //判断如果用户指标的结果为空或者指标结果数和指标个数不一致（未完全匹配），则不推送数据
        if (CollectionUtils.isEmpty(activityProfileResultPOS)
                || activityProfileResultPOS.size() < indicatorIds.size()) {
            log.warn("推送活动指标匹配结果异常actProfileId={},err=找不到对应的用户评估结果", profilePO.getAomActvId());
            return;
        }
        //计算用户各维度的得分
        BigDecimal userScore = BigDecimal.ZERO;
        for (ActivityProfileResultPO activityProfileResult : activityProfileResultPOS) {
            BigDecimal itemScore = qualifiedScoreConfigMap.getOrDefault(activityProfileResult.getQualified(),
                    BigDecimal.ZERO);
            userScore = userScore.add(itemScore);
        }
        //总分=设置达标得分*指标个数
        BigDecimal allTotalScore = totalScore.multiply(BigDecimal.valueOf(indicatorIds.size()));
        Actor actor = new Actor();
        actor.setUserId(userId);
        TargetObject targetObject = new TargetObject();
        targetObject.setTargetId(profilePO.getAomActvId());
        targetObject.setTargetType(UacdTypeEnum.ACTV_PROF.getRegId());
        targetObject.setSourceId(activity.getSourceId());
        targetObject.setSourceType(UacdTypeEnum.PRJ_XPD.getRegId());
        AssessmentActivityResult assessmentActivityResult = new AssessmentActivityResult();
        assessmentActivityResult.setResultStatus(2);
        assessmentActivityResult.setPassed(1);
        assessmentActivityResult.setScore(userScore);
        assessmentActivityResult.setTotalScore(allTotalScore);
        List<IndexResult> indexResults = new ArrayList<>();
        activityProfileResultPOS.forEach(activityProfileResultPO -> {
            IndexResult indexResult = new IndexResult();
            indexResult.setObjectiveModeId(profilePO.getModelId());
            indexResult.setObjectiveId(activityProfileResultPO.getSdIndicatorId());
            //维度类型:0-普通/1-能力/2-技能/3-知识/4-任务/5-绩效
            indexResult.setObjectiveType(indicatorIdTypeMap.get(activityProfileResultPO.getSdIndicatorId()));
            indexResult.setObjectiveScore(qualifiedScoreConfigMap.get(activityProfileResultPO.getQualified()));
            indexResult.setObjectiveTotalScore(totalScore);
            indexResult.setObjectiveResult(activityProfileResultPO.getQualified());
            if (activityProfileResultPO.getQualified() == 1) {
                indexResult.setObjectiveResult(3);
            }
            indexResults.add(indexResult);
        });
        assessmentActivityResult.setIndexResults(indexResults);
        log.info("推送动态人才评估结果数据给AOM，{}",
                BeanHelper.bean2Json(assessmentActivityResult, JsonInclude.Include.NON_NULL));
        resultRollUpService.rollUpAssessmentActivityResult(orgId, ActionEnum.COMPLETED, actor, targetObject,
                assessmentActivityResult);
    }

    private BigDecimal getTotalScore(String orgId, String actProfileId) {
        return activityProfileRepo.findById(orgId, actProfileId).getScoreQualified();
    }

    public void exportProfileUserInfo(String orgId, String actProfileId, String optUserId,
            ActProfileUserExportParam exportParam) {
        //查询活动指标数据
        Validate.isNotBlank(actProfileId, ExceptionKeys.PROFILE_PARAM_ID_EMPTY);
        ActivityProfilePO profilePO = activityProfileRepo.findById(orgId, actProfileId);
        Validate.isNotNull(profilePO, ExceptionKeys.PROFILE_NOT_FOUND);
        String prefixName = profilePO.getProfileName();
        if (profilePO.getProfileName().length() > 80) {
            prefixName = profilePO.getProfileName().substring(0, 80);
        }
        String fileName = prefixName + "评估结果";
        String fullname = "";
        Optional<User> userOpt = userDomainRepo.load(orgId, optUserId);
        if (userOpt.isPresent()) {
            fullname = userOpt.get().getFullname();
        }
        //动态表头,项目关联的指标
        List<ActivityProfileIndicatorPO> profileIndicatorPOList = activityProfileIndicatorRepo.findByActProfileId(orgId,
                actProfileId);
        List<String> indicatorIds = profileIndicatorPOList.stream().map(ActivityProfileIndicatorPO::getSdIndicatorId)
                .collect(Collectors.toList());
        //获取指标名称列表
        Map<String, String> customParam = new LinkedHashMap<>();
        customParam.put("fullname", "姓名");
        customParam.put("username", "账号");
        customParam.put("userStatus", "账号状态");
        customParam.put("deptName", "部门");
        customParam.put("positionName", "岗位");
        customParam.put("finishStatus", "完成状态");
        exportParam.setOrgId(orgId);
        exportParam.setActProfileId(actProfileId);
        //查询自定义维度信息
        if (CollectionUtils.isNotEmpty(indicatorIds)) {
            List<IndicatorDto> indicatorBaseDtos = spsdAclService.getIndicatorByModelId(orgId, profilePO.getModelId());
            Map<String, String> indicatorIdNameMap = indicatorBaseDtos.stream()
                    .collect(Collectors.toMap(IndicatorDto::getItemId, IndicatorDto::getIndicatorName));
            indicatorIds.forEach(
                    indicatorId -> customParam.put(indicatorId, indicatorIdNameMap.getOrDefault(indicatorId, "")));
        }
        activityProfileExportService.exportFile(exportParam, fileName, optUserId, fullname, customParam);
    }


    public String validateActivity(ActProfileCreateDTO actProfileCreateDTO) {
        if (StringUtils.isBlank(actProfileCreateDTO.getName())) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        if (StringUtils.isBlank(actProfileCreateDTO.getModelId())) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        if (Objects.isNull(actProfileCreateDTO.getUnqualifiedScore()) || Objects.isNull(
                actProfileCreateDTO.getQualifiedScore())) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        //        Validate.isNotBlank(actProfileCreateDTO.getName(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        //        Validate.isNotBlank(actProfileCreateDTO.getModelId(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        //        Validate.isNotNull(actProfileCreateDTO.getUnqualifiedScore(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        //        Validate.isNotNull(actProfileCreateDTO.getQualifiedScore(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        if (actProfileCreateDTO.getUnqualifiedScore().compareTo(BigDecimal.valueOf(999)) > 0
                || actProfileCreateDTO.getUnqualifiedScore().compareTo(BigDecimal.ZERO) < 0) {
            //            throw new ApiException(ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        if (actProfileCreateDTO.getQualifiedScore().compareTo(BigDecimal.valueOf(999)) > 0
                || actProfileCreateDTO.getQualifiedScore().compareTo(BigDecimal.ZERO) < 0) {
            //            throw new ApiException(ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        //达标分数不能小于不达标分数
        if (actProfileCreateDTO.getQualifiedScore().compareTo(actProfileCreateDTO.getUnqualifiedScore()) < 0) {
            //            throw new ApiException(ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        if (Objects.isNull(actProfileCreateDTO.getEvalTimeType())) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        //        Validate.isNotNull(actProfileCreateDTO.getEvalTimeType(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        if (actProfileCreateDTO.getEvalTimeType() == 2 && (Objects.isNull(actProfileCreateDTO.getEvalTime()))) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;

        }
        //        Validate.isNotEmpty(actProfileCreateDTO.getIndicators(), ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR);
        if (CollectionUtils.isEmpty(actProfileCreateDTO.getIndicators())) {
            return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
        }
        if (CollectionUtils.isNotEmpty(actProfileCreateDTO.getIndicators())) {
            boolean emptyValue = actProfileCreateDTO.getIndicators().stream().anyMatch(
                    el -> StringUtils.isBlank(el.getIndicatorRuleId()) || StringUtils.isBlank(el.getIndicatorId()));
            if (emptyValue) {
                return ExceptionKeys.ACTIVITY_PROFILE_PARAM_ERROR;
            }
        }
        return "";
    }


}