package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.spsdk.common.component.ExpressionCalc;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: geyan
 * @Date: 9/12/24 17:25
 * @Description:
 **/
@Data
public class XpdDimRuleDto {
    /**
     * 主键
     */
    private String id;

    /**
     * 上级维度规则id, 指向rv_xpd_dim_rule.id
     */
    private String parentId;

    /**
     * 冗余人才标准的维度id
     */
    private String sdDimId;

    /**
     * 计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算
     */
    private Integer calcType;

    /**
     * 权重, 当计算方式为<按子维度结果计算>有效
     */
    private BigDecimal weight;

    /**
     * 计算规则:0-快捷配置 1-高级公式
     */
    private Integer calcRule;

    /**
     * 计算规则表达式,当计算规则为<高级公式>时有效
     */
    private String formula;

    /**
     * 结果类型:0-分值 1-达标率,非<绩效维度>下有效
     */
    private Integer resultType;

    /**
     * 分层方式:0-按比例 1-按固定值
     */
    private Integer levelType;

    /**
     * 分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效
     */
    private Integer levelPriority;

    /**
     * 分层规则,json数组, 涉及到的维度分层规则id取自rv_xpd_grid_level.id
     */
    private List<DimGridLevelRuleDTO> gridLevelRules;
    private RatioLevelThresholdDto ruleThresholdDto;

    /**
     * 是否启用:0-未启用,1-启用
     */
    private Integer enabled;

    /**
     * 活动模型-活动ID
     */
    private String aomActId;
    /**
     * 单指标结果
     */
    private String sdIndicatorId;

    private ExpressionCalc formulaCalc;

    private BigDecimal calcTotalScore;
}
