package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CaliUserImportDbDTO {
    private String userId;
//    private Map<String,String> dmResult;
//    private Map<String,String> indicatorResult;

    // 校准后结果
    List<CaliUpdateUserResultReq> caliUpdateUserResultReq;

    //1: 按维度结果，2：按分层结果，3：按指标
    private int importType;

    //0-分值 1-达标率
    private int resultType;

    private String caliId;
    private String xpdId;

    @Schema(description = "发展建议")
    private String suggestion;
    @Schema(description = "校准原因")
    private String reason;

}
