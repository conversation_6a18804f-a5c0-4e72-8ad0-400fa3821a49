package com.yxt.talent.rv.application.calimeet.dto;

import com.yxt.talent.rv.application.xpd.common.dto.XpdDeptUserDimResultDTO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(description = "维度分层聚合结果")
public class XpdLevelAggDTO {

    @Schema(description = "维度/人才层级Id")
    private String levelId;

    @I18nTranslate(codeField = "levelNameI18n")
    @Schema(description = "维度/人才层级名称")
    private String levelName;

    @Schema(description = "维度/人才层级名称国际化code", hidden = true)
    private String levelNameI18n;

    @Schema(description = "维度/人才层级排序值")
    private Integer orderIndex;

    @Schema(description = "层级人数")
    private Integer userCnt = 0;

    @Schema(description = "盘点总人数")
    private Integer totalUserCnt;

    public XpdLevelAggDTO(XpdDeptUserDimResultDTO source) {
        this.levelId = source.getLevelId();
        this.levelName = source.getLevelName();
        this.levelNameI18n = source.getLevelNameI18n();
        this.orderIndex = source.getLevelOrderIndex();
        this.userCnt = source.getLevelUserCnt();
    }
}
