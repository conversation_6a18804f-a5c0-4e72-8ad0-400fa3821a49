package com.yxt.talent.rv.application.user.expt;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.user.dto.UserExtMsgDTO;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/4/21
 */
@Component
@RequiredArgsConstructor
public class UserExtErrExportStrategy extends AbstractExportStrategy {
    private final I18nComponent i18nComponent;
    private static final String HEADER_PREFIX0 = "apis.sptalentrv.user.ext.0.";
    private static final String[] HEADER_KEYS0 = new String[]{"title0", "title1", "title2", "title3", "title4",
            "username", "fullname", "managerStr", "gradeLevel", "profCerts","residenceAddress", "errMsg"};

    private static final String HEADER_PREFIX1 = "apis.sptalentrv.user.ext.1.";
    private static final String[] HEADER_KEYS1 = new String[]{"title0", "title1", "title2", "title3", "title4", "title5",
            "username", "fullname", "thirdDeptName", "thirdPositionName", "thirdJobgradeName", "actionName"
            , "occurrenceTime", "errMsg"};

    private static final String HEADER_PREFIX2 = "apis.sptalentrv.user.ext.2.";
    private static final String[] HEADER_KEYS2 = new String[]{"title0", "title1", "title2", "title3", "title4", "title5",
            "username", "fullname", "rpName", "rpTypeStr", "acqTimeStr", "pubFrom", "errMsg"};

    @Override
    public String write(String path, String fileName, Object data) throws IOException {
        String filePath = path + fileName;
        UserExtMsgDTO userExtMsg = (UserExtMsgDTO) data;
        Map<Integer, Map<String, Object>> headMap = new HashMap<>();
        // 多语言标题

        Map<String, Object> map0 = i18nComponent.getI18nMap(HEADER_PREFIX0, HEADER_KEYS0);
        headMap.put(0, map0);
        Map<String, Object> map1 = i18nComponent.getI18nMap(HEADER_PREFIX1, HEADER_KEYS1);
        headMap.put(1, map1);
        Map<String, Object> map2 = i18nComponent.getI18nMap(HEADER_PREFIX2, HEADER_KEYS2);
        headMap.put(2, map2);
        // data
        String templatePath = "excel/user_ext_err_export.xlsx";

        exportSheetsWithTemplate(headMap, userExtMsg, filePath, new ClassPathResource(templatePath).getInputStream());
        return fileName;
    }

    public void exportSheetsWithTemplate(
            Map<Integer, Map<String, Object>> headMap, UserExtMsgDTO userExtMsg, String filePath, InputStream templateInputStream
    ) throws IOException {
        // 创建文件对象
        File file = new File(filePath);
        boolean fileExist = file.exists();
        // 如果文件不存在则新建文件
        if (!fileExist) {
            if (!file.getCanonicalFile().getParentFile().exists()) {
                file.getCanonicalFile().getParentFile().mkdirs();
            }
            fileExist = file.createNewFile();
        }
        if (fileExist) {
            ExcelWriter excelWriter =
                    EasyExcel.write(new FileOutputStream(file)).withTemplate(templateInputStream).build();

            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();

            String sheetName0 = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.0.sheetName");
            WriteSheet writeSheet0 = EasyExcel.writerSheet(0, sheetName0).build();
            excelWriter.fill(userExtMsg.getUserBaseMsgList(), fillConfig, writeSheet0);

            Map<String, Object> headers0 = headMap.get(0);
            if (headers0 != null) {
                excelWriter.fill(headers0, writeSheet0);
            }

            String sheetName1 = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.1.sheetName");
            WriteSheet writeSheet1 = EasyExcel.writerSheet(1, sheetName1).build();
            excelWriter.fill(userExtMsg.getUserCareerHisList(), fillConfig, writeSheet1);
            Map<String, Object> headers1 = headMap.get(1);
            if (headers1 != null) {
                excelWriter.fill(headers1, writeSheet1);
            }

            String sheetName2 = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.2.sheetName");
            WriteSheet writeSheet2 = EasyExcel.writerSheet(2, sheetName2).build();
            excelWriter.fill(userExtMsg.getUserRewardList(), fillConfig, writeSheet2);
            Map<String, Object> headers2 = headMap.get(2);
            if (headers2 != null) {
                excelWriter.fill(headers2, writeSheet2);
            }

            excelWriter.finish();
        } else {
            throw new FileNotFoundException("file not exist!");
        }
    }

    @Override
    public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
        String taskName = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.import.err.filename");
        return buildDownInfo(userCache, fileName, taskName);
    }
}
