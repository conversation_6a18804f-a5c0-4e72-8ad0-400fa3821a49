package com.yxt.talent.rv.application.calimeet.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/29
 */
@Data
public class CaliMeetCreatDTO {
    /**
     * 校准任务名称
     */
    private String meetName;
    /**
     * 组织形式(0-在线校准，1-线下校准)
     */
    private Integer meetMode;
    /**
     * 开始时间
     */
    private LocalDateTime mtStartTime;
    /**
     * 结束时间
     */
    private LocalDateTime mtEndTime;
    /**
     * 组织人员
     */
    private List<String> organizeUserIds;
    /**
     * 校准人员
     */
    private List<String> caliUserIds;
    /**
     * 校准方式(0-维度分层结果，1-维度结果，2-指标结果)
     */
    private Integer meetType;
    /**
     * 是否开启比例控制(0-否，1-是)
     */
    private Integer showRatio;

    private String meetRecordId;
}
