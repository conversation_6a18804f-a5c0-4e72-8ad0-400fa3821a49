package com.yxt.talent.rv.application.xpd.actvimpt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.aom.base.service.part.impl.ActivityParticipationService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.downfacade.bean.down.DownInfo4Add;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.export.OutputStrategy;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.talent.rv.application.activity.dto.ActMemberUser;
import com.yxt.talent.rv.application.activity.dto.ActMemberUserCriteria;
import com.yxt.talent.rv.application.xpd.rule.XpdRuleAppService;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.cmd.XpdImportCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.cmd.XpdImportTempCmd;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.ImportGridLevelDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.dto.XpdImportUserDTO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportDimResult4Get;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportDimResust4VO;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportIndicatorResult4Get;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportUsers4Get;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityParticipationMemberMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpDeptPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.repository.aom.RvActivityParticipationMemberRepo;
import com.yxt.talent.rv.infrastructure.service.file.AbstractExportStrategy;
import com.yxt.talent.rv.infrastructure.service.file.ExcelUtils;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericApaasFileExportVO;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/12/18
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class XpdActvImptComponent {
    public static final BigDecimal MAX_999 = BigDecimal.valueOf(999);
    private final XpdImportMapper importMapper;
    private final SpsdAclService spsdAclService;
    private final XpdMapper xpdMapper;
    private final XpdImportLogMapper importLogMapper;
    private final XpdImportDimUserMapper importDimUserMapper;
    private final XpdImportIndicatorUserMapper importIndicatorUserMapper;
    private final XpdActvImptService importActService;
    private final I18nComponent i18nComponent;
    private final XpdActvImptDimTempStrategy importDimTempStrategy;
    private final XpdActvImptIndTempStrategy importIndTempStrategy;
    private final AuthService authService;
    private final XpdGridLevelMapper gridLevelMapper;
    private final RvActivityParticipationMemberMapper rvActivityParticipationMemberMapper;
    private final ActivityParticipationService activityParticipationService;
    private final RvActivityParticipationMemberRepo rvActivityParticipationMemberRepo;
    private final DlcComponent dlcComponent;
    private final XpdDimRuleMapper dimRuleMapper;
    private final RvActivityParticipationMemberMapper activityParticipationMemberMapper;
    private final UdpDeptMapper udpDeptMapper;
    private final XpdRuleAppService xpdRuleAppService;
    private final XpdGridLevelMapper xpdGridLevelMapper;


    public PagingList<ImportDimResult4Get> importList(String orgId, SearchDTO bean, PageRequest page) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEq = search.getFilterEq();
        String xpdId = filterEq.get("xpdId");

        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);

        Page<XpdImportPO> pageParam = ApiUtil.toPage(page);
        IPage<XpdImportPO> importPage = importMapper.selectPage(pageParam, orgId, xpdId);
        PagingList<XpdImportPO> importPagingList = BeanCopierUtil.toPagingList(importPage);

        PagingList<ImportDimResult4Get> resPage = new PagingList<>();
        resPage.setPaging(importPagingList.getPaging());
        if (CollectionUtils.isEmpty(importPagingList.getDatas())) {
            resPage.setDatas(new ArrayList<>());
            return resPage;
        }

        List<String> dimIds = importPagingList.getDatas().stream().map(XpdImportPO::getSdDimId).toList();
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), dimIds);
        if (CollectionUtils.isEmpty(dimInfoList)) {
            dimInfoList = new ArrayList<>();
        }
        Map<String, String> dimMap = StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId,
                ModelBase4Facade::getDmName);

        List<String> importIds = importPagingList.getDatas().stream().map(XpdImportPO::getId).toList();
        List<XpdImportLogPO> importLogList = importLogMapper.selectByImportId(orgId, importIds);
        Map<String, List<XpdImportLogPO>> importLogMap = importLogList.stream()
                .collect(Collectors.groupingBy(XpdImportLogPO::getImportId));

        Long partId = activityParticipationService.getParticipationId(orgId, xpd.getAomPrjId());
        // 项目人数
        long totalUserCount = 1;
        totalUserCount = activityParticipationMemberMapper.findTotalUserCount(orgId, xpd.getAomPrjId(), partId);

        // 导入人数
        // 维度
        List<XpdImportUserDTO> dimImportUsers = importDimUserMapper.selectUserCount(orgId, xpdId, importIds);
        Map<String, Integer> dimImportUserMap = StreamUtil.list2map(dimImportUsers, XpdImportUserDTO::getImportId,
                XpdImportUserDTO::getUserNum);
        // 指标
        List<XpdImportUserDTO> indiImportUsers = importIndicatorUserMapper.selectUserCount(orgId, xpdId, importIds);
        Map<String, Integer> indiImportUserMap = StreamUtil.list2map(indiImportUsers, XpdImportUserDTO::getImportId,
                XpdImportUserDTO::getUserNum);


        List<ImportDimResult4Get> resList = new ArrayList<>();
        for (XpdImportPO data : importPagingList.getDatas()) {
            ImportDimResult4Get res = new ImportDimResult4Get();
            res.setId(data.getId());
            //res.setSdDimId(data.getSdDimId());
            String dimName = dimMap.get(data.getSdDimId());
            if (dimName != null) {
                res.setName(dimName);
                AmSlDrawer4RespDTO amDimid = new AmSlDrawer4RespDTO();
                List<Object> datas = new ArrayList<>();
                Map<String, String> amDimMap = new HashMap<>();
                amDimMap.put("id", data.getSdDimId());
                amDimMap.put("name", dimName);
                datas.add(amDimMap);
                amDimid.setDatas(datas);
                res.setDimid(amDimid);
            }
            res.setImportype(String.valueOf(data.getImportType()));


            List<XpdImportLogPO> xpdImportLogList = importLogMap.get(data.getId());
            if (CollectionUtils.isNotEmpty(xpdImportLogList)) {
                Optional<XpdImportLogPO> xpdImportLog = xpdImportLogList.stream()
                        .max(Comparator.comparing(XpdImportLogPO::getCreateTime));
                xpdImportLog.ifPresent(xpdImportLogPO -> res.setImporttime(
                        DateTimeUtil.makeLocalDateTime2Date(xpdImportLogPO.getImportTime())));
                res.setStatus(String.valueOf(1));
            } else {
                res.setStatus(String.valueOf(0));
            }

            // 总人数

            // 导入人数
            Integer importType = data.getImportType();
            if (importType == 1) {
                Integer userNum = dimImportUserMap.get(data.getId());
                userNum = userNum == null ? 0 : userNum;
                res.setActvnumber(userNum + "/" + totalUserCount);
            } else {
                Integer userNum = indiImportUserMap.get(data.getId());
                userNum = userNum == null ? 0 : userNum;
                res.setActvnumber(userNum + "/" + totalUserCount);
            }
            resList.add(res);
        }
        resPage.setDatas(resList);
        return resPage;
    }

    public void create(String orgId, String userId, XpdImportCreateCmd cmd) {
        // 如果项目还没有设置任何规则，则不允许创建导入维度结果类型的活动
        List<XpdGridLevelPO> xpdGridLevels = xpdGridLevelMapper.listByXpdId(orgId, cmd.getXpdId());
        if (cmd.getImportType() == 1 && CollectionUtils.isEmpty(xpdGridLevels)) {
            throw new ApiException(ExceptionKeys.XPD_IMPT_GRID_LEVEL_NOT_EXIST);
        }
        // 检查维度是否已经有导入活动
        String dimId = cmd.getDimId();
        String xpdId = cmd.getXpdId();
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        Validate.isNotNull(xpd, "apis.sptalentrv.xpd.prj.not.exist");

        int count = importMapper.findCountBySdDimId(orgId, xpdId, dimId);
        if (count > 0) {
            throw new ApiException("apis.sptalentrv.xpd.import.dim.exist");
        }
        // 指标明细导入，必须: >=0 and <=999 的数值（包括小数）
        BigDecimal scoreTotal = cmd.getScoreTotal();
        if (cmd.getImportType() == 0) {
            if (MathUtil.isNonPositiveOrZero(scoreTotal)
                    || !MathUtil.isInRangeByType(scoreTotal, BigDecimal.ZERO, MAX_999, MathUtil.RangeType.CLOSED)) {
                throw new ApiException("apis.sptalentrv.xpd.import.ind.score.error");
            }
        }
        XpdImportPO xpdImport = new XpdImportPO();
        xpdImport.setId(ApiUtil.getUuid());
        xpdImport.setOrgId(orgId);
        xpdImport.setXpdId(xpdId);
        xpdImport.setSdDimId(cmd.getDimId());
        xpdImport.setImportType(cmd.getImportType());
        xpdImport.setScoreTotal(cmd.getScoreTotal());
        xpdImport.setDeleted(0);
        EntityUtil.setAuditFields(xpdImport, userId);

        // 当导入维度分层结果时，需要清空对应的维度规则
        if (cmd.getImportType() == 1) {
            xpdRuleAppService.deleteDimRuleWhenImport(orgId, userId, xpdId, dimId);
        }

        importMapper.insert(xpdImport);
    }

    public GenericApaasFileExportVO exportImportIndTemp(XpdImportTempCmd cmd, String userId, String orgId) {
        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(cmd.getImportId());
        Locale locale = authService.getLocale();
        // 拼接导出模板头部
        DynamicExcelExportContent content = importActService.dealImportTempExport(orgId, cmd.getImportId(), locale);
//        String path = dlcComponent.upload2TemporaryDisk(fileName, content, importIndTempStrategy);

        String fileName = getFileName(xpdImport);
        long taskId = dlcComponent.prepareExport(fileName, importIndTempStrategy);
        String path = dlcComponent.upload2Disk(fileName, content, importIndTempStrategy, taskId);

        return GenericApaasFileExportVO.byFilePath(path);
    }

    private OutputStrategy generateOutputStrategy(XpdImportPO xpdImport) {
        return new AbstractExportStrategy() {
            @Override
            public String write(String path, String fileName, Object data) throws IOException {
                String filePath = path + fileName;
                DynamicExcelExportContent results = (DynamicExcelExportContent) data;
                ExcelUtils.exportWithDynamicHeader(
                    results.getHeaders(), results.getSheets(), results.getData(), filePath);
                return fileName;
            }

            @Override
            public DownInfo4Add taskInfo(UserCacheDetail userCache, String fileName) {
                String taskName;
                if (xpdImport.getImportType() == 0) {
                    taskName = i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.export.temp.dim.indicator");
                } else {
                    taskName = i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.export.temp.dim.result");
                }
                return buildDownInfo(userCache, fileName, taskName);
            }
        };
    }


    private String getDimLockKey(String userId, String projectId) {
        return String.format(RedisKeys.LK_XPD_IMPORT_DIM_TEMP_EXPORT, projectId, userId);
    }

    private String getLockKey(String userId, String projectId) {
        return String.format(RedisKeys.LK_XPD_IMPORT_IND_TEMP_EXPORT, projectId, userId);
    }

    private String getFileName(XpdImportPO xpdImport) {
        Integer importType = xpdImport.getImportType();
        String date = DateTimeUtil.dateToString(new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS);
        if (importType == 0) {
            return i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.export.temp.dim.indicator") + date
                    + FileConstants.FILE_SUFFIX_XLSX;
        } else {
            return i18nComponent.getI18nValue("apis.sptalentrv.xpd.import.export.temp.dim.result") + date
                    + FileConstants.FILE_SUFFIX_XLSX;
        }

    }


    /**
     * 维度层级导出
     *
     * @param cmd
     * @param userId
     * @param orgId
     */
    public GenericApaasFileExportVO exportImportDimTemp(XpdImportTempCmd cmd, String userId, String orgId) {
        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(cmd.getImportId());
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdImport.getXpdId());
        List<String> levelNameList = gridLevelList.stream().map(XpdGridLevelPO::getLevelName).toList();

        // 拼接导出模板头部
        DynamicExcelExportContent content = importActService.dealExportDimLevel(orgId, xpdImport);
        Map<String, List<String>> fillMap = new HashMap<>();
        fillMap.put(FileConstants.SHEET_1, levelNameList);
        content.setFillData(fillMap);

        String fileName = getFileName(xpdImport);
//        String path = dlcComponent.upload2TemporaryDisk(fileName, content, importDimTempStrategy);
        long taskId = dlcComponent.prepareExport(fileName, importDimTempStrategy);
        String path = dlcComponent.upload2Disk(fileName, content, importDimTempStrategy, taskId);
        return GenericApaasFileExportVO.byFilePath(path);
    }


    public PagingList<ImportUsers4Get> userPage(String orgId, SearchDTO bean, PageRequest page) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEq = search.getFilterEq();
        Map<String, List<String>> filterIn = search.getFilterIn();
        String xpdId = filterEq.get("xpdId");
        String importId = filterEq.get("importId");

        String status = filterEq.get("numberstate");
        String positionId = filterEq.get("userid.positionId");

        List<String> deptIds = filterIn.get("userid.deptId");

        QueryUtil.SearchQuery searchLike = search.getSearchLike();
        String value = searchLike.getValue();
        value = ApiUtil.getFiltedLikeString(value);
        Page<ActMemberUser> pageParam = ApiUtil.toPage(page);


        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(importId);
        XpdPO xpdPO = xpdMapper.selectById(xpdImport.getXpdId());

        ActMemberUserCriteria searchParam = new ActMemberUserCriteria();
        // 部门，以及子部门
        if (CollectionUtils.isNotEmpty(deptIds)) {
            // 根据部门id ，找到所有子部门id
            List<UdpDeptPO> udpDeptPOS = udpDeptMapper.selectChildrenByDeptIds(orgId, deptIds);
            if (CollectionUtils.isNotEmpty(udpDeptPOS)) {
                List<String> childDeptIds = udpDeptPOS.stream().map(UdpDeptPO::getId).toList();
                searchParam.setDeptIds(childDeptIds);
            }

        }
        if (StringUtils.isNotBlank(positionId)) {
            searchParam.setPositionIds(Lists.newArrayList(positionId));
        }
        if (StringUtils.isNotBlank(status)) {
            searchParam.setStatus(Integer.parseInt(status));
        }


        searchParam.setKeyword(value);
        Long partId = activityParticipationService.getParticipationId(orgId, xpdPO.getAomPrjId());
        searchParam.setParticipationId(partId);
        searchParam.setActvId(xpdPO.getAomPrjId());
        searchParam.setPrjId(xpdPO.getAomPrjId());
        IPage<ActMemberUser> pageData = rvActivityParticipationMemberMapper.listPrjUserPage(pageParam, orgId,
                searchParam);
        PagingList<ActMemberUser> pagingList = BeanCopierUtil.toPagingList(pageData);
        PagingList<ImportUsers4Get> resPage = new PagingList<>();

        if (CollectionUtils.isEmpty(pagingList.getDatas())) {
            resPage.setPaging(pagingList.getPaging());
            resPage.setDatas(new ArrayList<>());
            return resPage;
        }

        // 获取维度结果
        List<String> userIds = pagingList.getDatas().stream().map(ActMemberUser::getUserId).toList();
        // 维度结果
        List<ImportGridLevelDTO> userGridLevelList = importDimUserMapper.findUserGridLevel(orgId, xpdId,
                xpdImport.getSdDimId(), importId, userIds);
        Map<String, String> userLevelMap = StreamUtil.list2map(userGridLevelList, ImportGridLevelDTO::getUserId,
                ImportGridLevelDTO::getGridLevelName);
        List<ImportUsers4Get> resList = new ArrayList<>();
        for (ActMemberUser data : pagingList.getDatas()) {
            ImportUsers4Get res = new ImportUsers4Get();
            res.setUserid(
                    ApassEntityUtils.createDrawer4UserDTO(data.getFullname(), data.getUserId(), data.getUsername(),
                            data.getDeptName(), String.valueOf(data.getStatus()), data.getPositionName(), null));
            res.setNumberstate(String.valueOf(data.getStatus()));

            String levelName = userLevelMap.get(data.getUserId());
            if (StringUtils.isBlank(levelName)) {
                resList.add(res);
                continue;
            }
            // 等级
            AmSlDrawer4RespDTO respDTO = new AmSlDrawer4RespDTO();
            List<Object> datas = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("id", data.getUserId());
            map.put("name", levelName);
            datas.add(map);
            respDTO.setDatas(datas);
            res.setGridlevelid(respDTO);
            resList.add(res);
        }
        resPage.setDatas(resList);
        resPage.setPaging(pagingList.getPaging());
        return resPage;
    }


    /**
     * 维度指标结果列表
     *
     * @param orgId
     * @param page
     * @param bean
     * @return
     */
    public PagingList<ImportIndicatorResult4Get> userIndicatorResult(String orgId, PageRequest page, SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String userId = search.getEqVal("userId");
        String importId = search.getEqVal("importId");

        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(importId);
        XpdPO xpdPO = xpdMapper.selectById(xpdImport.getXpdId());
        PagingList<ImportIndicatorResult4Get> resPage = new PagingList<>();
        Page<XpdImportIndicatorUserPO> pageParam = ApiUtil.toPage(page);
        IPage<XpdImportIndicatorUserPO> indicatorUserPage = importIndicatorUserMapper.selectByUserIdsPage(pageParam,
                orgId, xpdImport.getXpdId(), xpdImport.getId(), Lists.newArrayList(userId));
        PagingList<XpdImportIndicatorUserPO> pagingList = BeanCopierUtil.toPagingList(indicatorUserPage);


        //        List<IndicatorDto> lastIndicators =
        //            spsdAclService.getLastIndicatorByDims(orgId, xpdPO.getModelId(), xpdImport.getSdDimId());
        List<IndicatorDto> lastIndicators = spsdAclService.getIndicatorByModelId(orgId, xpdPO.getModelId());
        Map<String, IndicatorDto> indicatorMap = StreamUtil.list2map(lastIndicators, IndicatorDto::getItemId);

        /*IndicatorDimReq req = new IndicatorDimReq();
        req.setOrgId(orgId);
        req.setModelId(xpdPO.getModelId());
        req.setDimIds(Lists.newArrayList(xpdImport.getSdDimId()));
        Map<String, IndicatorDto> indicatorMap = spsdAclService.getLastIndicatorByDims(req);*/
        //log.info("userIndicatorResult indicatorMap={}", indicatorMap);

        List<ImportIndicatorResult4Get> resList = new ArrayList<>();
        for (XpdImportIndicatorUserPO data : pagingList.getDatas()) {
            ImportIndicatorResult4Get indicatorResult = new ImportIndicatorResult4Get();
            indicatorResult.setSdindicatorscore(data.getSdIndicatorScore());
            /*indicatorResult.setQualified(data.getQualified() == 0 ? i18nComponent.getI18nValue("apis.sptalentrv.xpd.act.import.indicator.standard.no") :
                i18nComponent.getI18nValue("apis.sptalentrv.xpd.act.import.indicator.standard.yes"));*/
            indicatorResult.setQualified(String.valueOf(data.getQualified()));
            IndicatorDto indicatorDto = indicatorMap.get(data.getSdIndicatorId());
            if (indicatorDto != null) {
                indicatorResult.setIndicatorid(ApassEntityUtils.createAmSlDrawerIdName(data.getSdIndicatorId(),
                        indicatorDto.getIndicatorName()));
                indicatorResult.setName(indicatorDto.getIndicatorName());
            }
            resList.add(indicatorResult);
        }
        resPage.setDatas(resList);
        resPage.setPaging(pagingList.getPaging());

        return resPage;
    }

    public void deleteImportAct(String orgId, String importId, String userId) {
        XpdImportPO importPO = importMapper.selectByPrimaryKey(importId);

        importPO.setDeleted(1);
        EntityUtil.setUpdate(importPO, userId);
        importMapper.insertOrUpdate(importPO);
        // 恢复规则
        dimRuleMapper.setRuleEnable(orgId, importPO.getXpdId(), importPO.getSdDimId(), userId);
    }

    public void removeUserData(String orgId, String importId, String userId, String operator) {

        XpdImportPO importPO = importMapper.selectByPrimaryKey(importId);
        // 维度层级
        importDimUserMapper.deleteByImportIdAndUserId(orgId, importId, importPO.getSdDimId(), userId, operator);
        // 指标数据
        importIndicatorUserMapper.deleteByImportIdAndUserId(orgId, importId, userId, operator);

    }

    public ImportDimResust4VO getUserCount(String orgId, String importId) {
        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(importId);
        XpdPO xpdPO = xpdMapper.selectById(xpdImport.getXpdId());
        int userNum = 0;
        // 总人数
        Long partId = activityParticipationService.getParticipationId(orgId, xpdPO.getAomPrjId());
        long totalNum = rvActivityParticipationMemberRepo.findTotalUserCount(orgId, xpdPO.getAomPrjId(), partId);

        if (xpdImport.getImportType() == 0) {
            List<XpdImportUserDTO> xpdImportUsers = importIndicatorUserMapper.selectUserCount(orgId,
                    xpdImport.getXpdId(), Lists.newArrayList(xpdImport.getId()));
            if (CollectionUtils.isNotEmpty(xpdImportUsers)) {
                XpdImportUserDTO xpdImportUserDTO = xpdImportUsers.get(0);
                userNum = xpdImportUserDTO.getUserNum();
            }
        } else {
            List<XpdImportUserDTO> xpdImportUsers = importDimUserMapper.selectUserCount(orgId, xpdImport.getXpdId(),
                    Lists.newArrayList(xpdImport.getId()));
            if (CollectionUtils.isNotEmpty(xpdImportUsers)) {
                XpdImportUserDTO xpdImportUserDTO = xpdImportUsers.get(0);
                userNum = xpdImportUserDTO.getUserNum();

            }
        }
        ImportDimResust4VO res = new ImportDimResust4VO();
        // 维度名称
        Map<String, ModelBase4Facade> dimInfoMap = spsdAclService.getDimInfoMap(orgId, xpdPO.getModelId(),
                Lists.newArrayList(xpdImport.getSdDimId()));
        ModelBase4Facade modelBase4Facade = dimInfoMap.get(xpdImport.getSdDimId());
        if (modelBase4Facade != null) {
            AmSlDrawer4RespDTO dimid = new AmSlDrawer4RespDTO();
            List<Object> datas = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("id", xpdImport.getSdDimId());
            map.put("name", modelBase4Facade.getDmName());
            datas.add(map);
            dimid.setDatas(datas);
            res.setDimid(dimid);
            //res.setDimId(dimid);
        }
        res.setImportype(String.valueOf(xpdImport.getImportType()));
        res.setImportType("0");
        res.setActvnumber(userNum + "/" + totalNum);
        return res;
    }

    public ImportDimResust4VO getImportMsg(String orgId, String importId) {
        XpdImportPO xpdImport = importMapper.selectByPrimaryKey(importId);
        XpdPO xpdPO = xpdMapper.selectById(xpdImport.getXpdId());
        Validate.isNotNull(xpdPO, ExceptionKeys.XPD_NOT_EXIST);
        int userNum = 0;
        // 总人数
        Long partId = activityParticipationService.getParticipationId(orgId, xpdPO.getAomPrjId());
        long totalNum = rvActivityParticipationMemberRepo.findTotalUserCount(orgId, xpdPO.getAomPrjId(), partId);

        if (xpdImport.getImportType() == 0) {
            List<XpdImportUserDTO> xpdImportUsers = importIndicatorUserMapper.selectUserCount(orgId,
                    xpdImport.getXpdId(), Lists.newArrayList(xpdImport.getId()));
            if (CollectionUtils.isNotEmpty(xpdImportUsers)) {
                XpdImportUserDTO xpdImportUserDTO = xpdImportUsers.get(0);
                userNum = xpdImportUserDTO.getUserNum();
            }
        } else {
            List<XpdImportUserDTO> xpdImportUsers = importDimUserMapper.selectUserCount(orgId, xpdImport.getXpdId(),
                    Lists.newArrayList(xpdImport.getId()));
            if (CollectionUtils.isNotEmpty(xpdImportUsers)) {
                XpdImportUserDTO xpdImportUserDTO = xpdImportUsers.get(0);
                userNum = xpdImportUserDTO.getUserNum();

            }
        }
        ImportDimResust4VO res = new ImportDimResust4VO();
        // 维度名称
        Map<String, ModelBase4Facade> dimInfoMap = spsdAclService.getDimInfoMap(orgId, xpdPO.getModelId(),
                Lists.newArrayList(xpdImport.getSdDimId()));
        ModelBase4Facade modelBase4Facade = dimInfoMap.get(xpdImport.getSdDimId());
        if (modelBase4Facade != null) {
            AmSlDrawer4RespDTO dimid = new AmSlDrawer4RespDTO();
            List<Object> datas = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("id", xpdImport.getSdDimId());
            map.put("name", modelBase4Facade.getDmName());
            datas.add(map);
            Map<String, AmSlDrawer4RespDTO> resMap = new HashMap<>();
            AmSlDrawer4RespDTO temp = new AmSlDrawer4RespDTO();
            temp.setDatas(datas);
            resMap.put("@dimid", temp);

            Map<String, String> nameMap = new HashMap<>();
            nameMap.put("id", xpdImport.getSdDimId());
            nameMap.put("name", modelBase4Facade.getDmName());

            dealDatas(resMap, res, nameMap);
            /*//res.setDimid(dimid);
            res.setDimId(dimid);*/
        }
        res.setImportype(String.valueOf(xpdImport.getImportType()));
        res.setImportType("0");
        res.setActvnumber(userNum + "/" + totalNum);
        return res;
    }

    private void dealDatas(Map<String, AmSlDrawer4RespDTO> resMap, ImportDimResust4VO res,
            Map<String, String> nameMap) {
        List<Object> datas = new ArrayList<>();
        //datas.add(resMap);
        datas.add(nameMap);
        AmSlDrawer4RespDTO temp = new AmSlDrawer4RespDTO();
        temp.setDatas(datas);
        res.setDimid(temp);
    }


}
