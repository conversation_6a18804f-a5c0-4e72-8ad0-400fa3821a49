package com.yxt.talent.rv.application.user;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.google.protobuf.Api;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.ILock;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.DateUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.downfacade.dto.File4CreateFinishMq;
import com.yxt.export.DlcComponent;
import com.yxt.export.ExcelUtil;
import com.yxt.export.ExportMqService;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.user.dto.UserBaseMsgDTO;
import com.yxt.talent.rv.application.user.dto.UserCareerHisDTO;
import com.yxt.talent.rv.application.user.dto.UserExtMsgDTO;
import com.yxt.talent.rv.application.user.dto.UserRewardDTO;
import com.yxt.talent.rv.application.user.expt.UserExtErrExportStrategy;
import com.yxt.talent.rv.application.user.expt.UserExtTempExportStrategy;
import com.yxt.talent.rv.controller.manage.xpd.actvimpt.viewobj.ImportResultVO;
import com.yxt.talent.rv.controller.talentcore.command.UserExtImportCmd;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.career.CareerHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.reward.RewardPunishmentHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.UserExtMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.CareerHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.RewardPunishmentHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserExtPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericApaasFileExportVO;
import com.yxt.talent.rv.infrastructure.service.remote.FileAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/4/17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserMsgImptService {
    private final ILock lockService;
    private final FileAclService fileAclService;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final I18nComponent i18nComponent;
    private final DlcComponent dlcComponent;

    private final UserExtErrExportStrategy userExtErrExportStrategy;
    private final RewardPunishmentHistoryMapper rewardPunishmentHistoryMapper;
    private final UserExtMapper userExtMapper;
    private final CareerHistoryMapper careerHistoryMapper;
    private final UserExtTempExportStrategy userExtTempExportStrategy;

    public static final int MAX_LEASE_TIME = 120;

    public GenericApaasFileExportVO userExtTempExport(String orgId) {

        String fileName = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.import.temp.filename")
                          + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;
        long taskId = dlcComponent.prepareExport(fileName, userExtTempExportStrategy);
        String path = dlcComponent.upload2Disk(fileName, null, userExtTempExportStrategy, taskId);
        GenericApaasFileExportVO res = new GenericApaasFileExportVO();
        res.setFilePath(path);
        return res;
    }

    public ImportResultVO userExtImport(
        String orgId , UserExtImportCmd cmd, MultipartFile file, String userId) {
        String lockKey = String.format(RedisKeys.CACHE_TALENT_USER_EXT_IMPORT, orgId, userId);
        ImportResultVO importResult = new ImportResultVO();
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                InputStream fileStream = getFileStream(file, cmd);
                UserExtMsgDTO userExtMsg = readExcel(fileStream);
                importResult.setTotalCount(userExtMsg.getUserBaseMsgList().size() + userExtMsg.getUserCareerHisList().size() + userExtMsg.getUserRewardList().size());
                // 检测
                chkUserExt(orgId, userExtMsg);
                // 导出错误数据
                dealErrMsg(userExtMsg, importResult);
                // 保存数据
                saveImport(orgId, null, userExtMsg, importResult);

            } catch (Exception e) {
                log.error("userExtImport error={}", e);
            } finally {
                lockService.unLock(lockKey);
            }

        }
        return importResult;
    }

    private void saveImport(String orgId, String userId, UserExtMsgDTO userExtMsg, ImportResultVO importResult) {
        List<UserBaseMsgDTO> userBaseMsgList = userExtMsg.getUserBaseMsgList();
        userBaseMsgList  = userBaseMsgList.stream().filter(x-> StringUtils.isBlank(x.getErrMsg())).toList();
        List<UserCareerHisDTO> userCareerHisList =
            userExtMsg.getUserCareerHisList().stream().filter(x -> StringUtils.isBlank(x.getErrMsg())).toList();
        userBaseMsgList  = userBaseMsgList.stream().filter(x-> StringUtils.isBlank(x.getErrMsg())).toList();
        List<UserRewardDTO> userRewardList =
            userExtMsg.getUserRewardList().stream().filter(x -> StringUtils.isBlank(x.getErrMsg())).toList();

        List<String> userIds = new ArrayList<>();
        userIds.addAll(userCareerHisList.stream().map(UserCareerHisDTO::getUserId).toList());
        userIds.addAll(userBaseMsgList.stream().map(UserBaseMsgDTO::getUserId).toList());
        userIds.addAll(userRewardList.stream().map(UserRewardDTO::getUserId).toList());

        List<UdpLiteUserPO> udpLiteUserPOS = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        Map<String, UdpLiteUserPO> userMap = StreamUtil.list2map(udpLiteUserPOS, UdpLiteUserPO::getId);

        List<String> inUserIds = userBaseMsgList.stream().map(UserBaseMsgDTO::getUserId).toList();
        List<UserExtPO> userExtList = new ArrayList<>();

        List<UserExtPO> exisUserExtList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(inUserIds)) {
            exisUserExtList = userExtMapper.selectByAndUserIds(orgId, inUserIds);
        }

        Map<String, UserExtPO> userExtMap = StreamUtil.list2map(exisUserExtList, UserExtPO::getUserId);
        if (CollectionUtils.isNotEmpty(userBaseMsgList)) {
            for (UserBaseMsgDTO userBaseMsg : userBaseMsgList) {
                String inUserId = userBaseMsg.getUserId();
                UserExtPO userExtPO1 = userExtMap.get(inUserId);
                if (userExtPO1 != null) {
                    userExtPO1.setManager(userBaseMsg.getManager());
                    userExtPO1.setGradeLevel(userBaseMsg.getGradeLevel());
                    userExtPO1.setProfCerts(userBaseMsg.getProfCerts());
                    userExtPO1.setResidenceAddress(userBaseMsg.getResidenceAddress());
                    userExtList.add(userExtPO1);
                } else {
                    UdpLiteUserPO udpLiteUserPO = userMap.get(userBaseMsg.getUserId());
                    String thirdUserId = udpLiteUserPO.getThirdUserId();
                    String newId = ApiUtil.getUuid();
                    UserExtPO userExtPO = new UserExtPO();
                    userExtPO.setId(newId);
                    userExtPO.setOrgId(orgId);
                    userExtPO.setThirdUserId(thirdUserId);
                    userExtPO.setEnabled(1);
                    userExtPO.setUserId(userBaseMsg.getUserId());
                    userExtPO.setManager(userBaseMsg.getManager());
                    userExtPO.setGradeLevel(userBaseMsg.getGradeLevel());
                    userExtPO.setResidenceAddress(userBaseMsg.getResidenceAddress());
                    userExtPO.setProfCerts(userBaseMsg.getProfCerts());
                    userExtPO.setDeleted(0);
                    userExtList.add(userExtPO);
                }




            }
        }
        List<CareerHistoryPO> careerHistoryList = new ArrayList<>();
        for (UserCareerHisDTO userCareerHisDTO : userCareerHisList) {
            CareerHistoryPO careerHistoryPO = new CareerHistoryPO();
            UdpLiteUserPO udpLiteUserPO = userMap.get(userCareerHisDTO.getUserId());

            String thirdUserId = udpLiteUserPO.getThirdUserId();
            String thirdCareerHistoryId = ApiUtil.getUuid();
            String newCareerId = ApiUtil.getUuid();
            careerHistoryPO.setId(newCareerId);
            careerHistoryPO.setOrgId(orgId);
            careerHistoryPO.setUserId(userCareerHisDTO.getUserId());
            careerHistoryPO.setThirdUserId(thirdUserId);
            careerHistoryPO.setThirdDeptName(userCareerHisDTO.getThirdDeptName());
            careerHistoryPO.setThirdPositionName(userCareerHisDTO.getThirdPositionName());
            careerHistoryPO.setThirdJobgradeName(userCareerHisDTO.getThirdJobgradeName());
            careerHistoryPO.setActionName(userCareerHisDTO.getActionName());
            careerHistoryPO.setOccurrenceTime(userCareerHisDTO.getLocalOccurrenceTime());
            careerHistoryPO.setDeleted(0);
            careerHistoryPO.setCreateTime(LocalDateTime.now());
            careerHistoryPO.setUpdateTime(LocalDateTime.now());
            careerHistoryPO.setThirdCareerHistoryId(thirdCareerHistoryId);
            careerHistoryList.add(careerHistoryPO);
        }

        List<RewardPunishmentHistoryPO> rewardPunishmentHistoryList = new ArrayList<>();
        for (UserRewardDTO userRewardDTO : userRewardList) {
            RewardPunishmentHistoryPO rewardPunishmentHistoryPO = new RewardPunishmentHistoryPO();
            UdpLiteUserPO udpLiteUserPO = userMap.get(userRewardDTO.getUserId());
            if (udpLiteUserPO == null) {
                continue;
            }

            /*String rewardId = MD5.create().digestHex(orgId + udpLiteUserPO.getThirdUserId() + userRewardDTO.getRpType() +
                                                     userRewardDTO.getAcqTime() + userRewardDTO.getRpName());*/
            String rewardId = ApiUtil.getUuid();
            rewardPunishmentHistoryPO.setId(rewardId);
            rewardPunishmentHistoryPO.setOrgId(orgId);
            rewardPunishmentHistoryPO.setUserId(userRewardDTO.getUserId());
            rewardPunishmentHistoryPO.setThirdUserId(udpLiteUserPO.getThirdUserId());
            rewardPunishmentHistoryPO.setRpType(userRewardDTO.getRpType());
            rewardPunishmentHistoryPO.setRpName(userRewardDTO.getRpName());
            rewardPunishmentHistoryPO.setAcqTime(userRewardDTO.getAcqTime().toLocalDate());
            rewardPunishmentHistoryPO.setPubFrom(userRewardDTO.getPubFrom());
            rewardPunishmentHistoryPO.setDeleted(0);
            rewardPunishmentHistoryPO.setCreateTime(LocalDateTime.now());
            rewardPunishmentHistoryPO.setUpdateTime(LocalDateTime.now());
            rewardPunishmentHistoryList.add(rewardPunishmentHistoryPO);
        }

        importResult.setSuccessCount(userExtList.size() + rewardPunishmentHistoryList.size() + careerHistoryList.size());
        if (CollectionUtils.isNotEmpty(userExtList)) {
            userExtMapper.insertOrUpdateBatch(userExtList);
        }
        if (CollectionUtils.isNotEmpty(rewardPunishmentHistoryList)) {
            rewardPunishmentHistoryMapper.insertOrUpdateBatch(rewardPunishmentHistoryList);
        }
        if (CollectionUtils.isNotEmpty(careerHistoryList)) {
            careerHistoryMapper.insertOrUpdateBatch(careerHistoryList);
        }

    }

    private void dealErrMsg(UserExtMsgDTO userExtMsg, ImportResultVO importResult) {
        List<UserBaseMsgDTO> userBaseMsgErrList =
            userExtMsg.getUserBaseMsgList().stream().filter(x -> StringUtils.isNotEmpty(x.getErrMsg())).toList();

        List<UserRewardDTO> userRewardErrList = userExtMsg.getUserRewardList().stream()
            .filter(x -> StringUtils.isNotEmpty(x.getErrMsg())).toList();

        List<UserCareerHisDTO> userCareerHisErrList =
            userExtMsg.getUserCareerHisList().stream().filter(x -> StringUtils.isNotEmpty(x.getErrMsg())).toList();

        if (CollectionUtils.isEmpty(userBaseMsgErrList)
            && CollectionUtils.isEmpty(userRewardErrList)
            && CollectionUtils.isEmpty(userCareerHisErrList)) {
            importResult.setErrorCode(0);
            importResult.setFailCount(0);
            return;
        }
        UserExtMsgDTO export = new UserExtMsgDTO();
        export.setUserBaseMsgList(userBaseMsgErrList);
        export.setUserCareerHisList(userCareerHisErrList);
        export.setUserRewardList(userRewardErrList);

        String fileName = i18nComponent.getI18nValue("apis.sptalentrv.user.ext.import.err.filename")
                          + System.currentTimeMillis() + FileConstants.FILE_SUFFIX_XLSX;

        String downFileName = fileName + FileConstants.FILE_SUFFIX_XLSX + FileConstants.ORIG;

        long taskId = dlcComponent.prepareExport(fileName, userExtErrExportStrategy);
        String path = dlcComponent.upload2Disk(fileName, export, userExtErrExportStrategy, taskId);
        importResult.setFailedCount(userBaseMsgErrList.size() + userRewardErrList.size() + userCareerHisErrList.size());
        importResult.setFilePath("/down/#/download/");
    }

    private void chkUserExt(String orgId, UserExtMsgDTO userExtMsg) {
        List<String> userNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userExtMsg.getUserBaseMsgList())) {
            userNames.addAll(
                userExtMsg.getUserBaseMsgList()
                    .stream().map(UserBaseMsgDTO::getUsername)
                    .filter(StringUtils::isNotEmpty).toList()
            );

        }

        if (CollectionUtils.isNotEmpty(userExtMsg.getUserRewardList())) {
            userNames.addAll(
                userExtMsg.getUserRewardList()
                    .stream().map(UserRewardDTO::getUsername)
                    .filter(StringUtils::isNotEmpty).toList()
            );

        }

        if (CollectionUtils.isNotEmpty(userExtMsg.getUserCareerHisList())) {
            userNames.addAll(
                userExtMsg.getUserCareerHisList()
                    .stream().map(UserCareerHisDTO::getUsername)
                    .filter(StringUtils::isNotEmpty).toList()
            );
        }

        userNames = userNames.stream().distinct().toList();

        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserNames(orgId, userNames);
        Map<String, String> userMap =
            StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getUsername, UdpLiteUserPO::getId);
        // 人才信息
        chkUserBase(userExtMsg, userMap);

        // 内部任职履历
        chkUserCareer(userExtMsg, userMap);

        // 奖励信息
        // 奖罚类型
        chkReward(userExtMsg, userMap);


    }

    private void chkReward(UserExtMsgDTO userExtMsg, Map<String, String> userMap) {
        String rpTypeYes = getI18Msg("apis.sptalentrv.user.ext.rpTypeStr.yes");

        String rpTypeNo = getI18Msg("apis.sptalentrv.user.ext.rpTypeStr.no");
        for (UserRewardDTO userReward : userExtMsg.getUserRewardList()) {
            if (StringUtils.isBlank(userReward.getUsername())) {
                userReward.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.empty"));
                continue;
            }
            if (!userMap.containsKey(userReward.getUsername())) {
                userReward.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.exist"));
                continue;
            } else {
                userReward.setUserId(userMap.get(userReward.getUsername()));
            }
            if (StringUtils.isBlank(userReward.getRpName())) {
                userReward.setErrMsg(getI18Msg("apis.sptalentrv.user.ext.rpName.empty"));
                continue;
            } else if (userReward.getRpName().length() > 200){
                userReward.setRpName(getSubstring(userReward.getRpName(), 200));
            }
            if (StringUtils.isBlank(userReward.getRpTypeStr())) {
                userReward.setErrMsg(getI18Msg("apis.sptalentrv.user.ext.rpTypeStr.empty"));
            } else {
                String rpTypeStr = userReward.getRpTypeStr();
                if (!rpTypeStr.equals(rpTypeYes) && !rpTypeStr.equals(rpTypeNo)) {
                    userReward.setErrMsg(getI18Msg("apis.sptalentrv.user.ext.rpTypeStr.error"));
                    continue;
                } else {
                    if (rpTypeStr.equals(rpTypeYes)) {
                        userReward.setRpType(1);
                    } else {
                        userReward.setRpType(2);
                    }
                }
            }
            if (StringUtils.isBlank(userReward.getAcqTimeStr())) {
                userReward.setErrMsg(getI18Msg("apis.sptalentrv.user.ext.reward.acqTimeStr.empty"));
                continue;
            }

            if (!DateTimeUtil.chkDateFormate(userReward.getAcqTimeStr())) {
                userReward.setErrMsg(getI18Msg("apis.sptalentrv.user.ext.reward.acqTimeStr.error"));
                continue;
            } else {
                userReward.setAcqTime(DateTimeUtil.parseFlexibleDate(userReward.getAcqTimeStr()));
            }

            if (StringUtils.isNotEmpty(userReward.getPubFrom())) {
                userReward.setPubFrom(getSubstring(userReward.getPubFrom(), 200));
            }

        }
    }

    private void chkUserCareer(UserExtMsgDTO userExtMsg, Map<String, String> userMap) {
        List<String> actNames = new ArrayList<>();
        actNames.add(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.actionName.joining"));
        actNames.add(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.actionName.promotion"));
        actNames.add(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.actionName.transfer"));
        for (UserCareerHisDTO userCareerHis : userExtMsg.getUserCareerHisList()) {
            if (StringUtils.isBlank(userCareerHis.getUsername())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.empty"));
                continue;
            }
            if (!userMap.containsKey(userCareerHis.getUsername())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.exist"));
                continue;
            } else {
                userCareerHis.setUserId(userMap.get(userCareerHis.getUsername()));
            }

            if (StringUtils.isBlank(userCareerHis.getThirdDeptName())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.dept.empty"));
                continue;
            } else if (userCareerHis.getThirdDeptName().length() > 200) {
                userCareerHis.setThirdDeptName(getSubstring(userCareerHis.getThirdDeptName(), 200));

            }
            if (StringUtils.isNotEmpty(userCareerHis.getThirdPositionName()) &&
                userCareerHis.getThirdPositionName().length() > 200) {
                userCareerHis.setThirdPositionName(getSubstring(userCareerHis.getThirdPositionName(), 200));
            }

            if (StringUtils.isNotEmpty(userCareerHis.getThirdJobgradeName()) &&
                userCareerHis.getThirdJobgradeName().length() > 200) {
                userCareerHis.setThirdJobgradeName(getSubstring(userCareerHis.getThirdJobgradeName(), 200));
            }

            if (StringUtils.isBlank(userCareerHis.getActionName())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.actionName.empty"));
                continue;
            } else if (!actNames.contains(userCareerHis.getActionName())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.actionName.error"));
                continue;
            }
            if (StringUtils.isBlank(userCareerHis.getOccurrenceTime())) {
                userCareerHis.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.occurrenceTime.empty"));
                continue;
            } else if (!DateTimeUtil.chkDateFormate(userCareerHis.getOccurrenceTime())) {
                userCareerHis.setErrMsg(
                    i18nComponent.getI18nValue("apis.sptalentrv.user.ext.occurrenceTime.formate.error"));
                continue;
            } else {
                userCareerHis.setLocalOccurrenceTime(DateTimeUtil.parseFlexibleDate(userCareerHis.getOccurrenceTime()));
            }


        }
    }

    private void chkUserBase(UserExtMsgDTO userExtMsg, Map<String, String> userMap) {
        String manageYes = getI18Msg("apis.sptalentrv.user.ext.manager.yes");
        String manageNo = getI18Msg("apis.sptalentrv.user.ext.manager.no");
        List<UserBaseMsgDTO> userBaseMsgList = userExtMsg.getUserBaseMsgList();
        // 排除重复账号
        List<String> usernameList = userBaseMsgList.stream()
            .map(UserBaseMsgDTO::getUsername)
            .filter(username -> Collections.frequency(
                userBaseMsgList.stream().map(UserBaseMsgDTO::getUsername).toList(), username) > 1)
            .toList();
        for (UserBaseMsgDTO userBaseMsg : userExtMsg.getUserBaseMsgList()) {
            if (usernameList.contains(userBaseMsg.getUsername())) {
                userBaseMsg.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.repeat"));
                continue;
            }
            if (StringUtils.isBlank(userBaseMsg.getUsername())) {
                userBaseMsg.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.empty"));
                continue;
            }
            if (!userMap.containsKey(userBaseMsg.getUsername())) {
                userBaseMsg.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.username.exist"));
                continue;
            } else {
                userBaseMsg.setUserId(userMap.get(userBaseMsg.getUsername()));
            }

            String managerStr = userBaseMsg.getManagerStr();
            if (StringUtils.isBlank(managerStr)) {
                userBaseMsg.setManager(0);
                /*userBaseMsg.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.manager.empty"));
                continue;*/
            } else if (!managerStr.equals(manageYes) && !managerStr.equals(manageNo)) {
                userBaseMsg.setErrMsg(i18nComponent.getI18nValue("apis.sptalentrv.user.ext.manager.error"));
                continue;
            } else {
                userBaseMsg.setManager(managerStr.equals(manageYes) ? 1 : 0);
            }

            if (StringUtils.isNotEmpty(userBaseMsg.getGradeLevel()) && userBaseMsg.getGradeLevel().length() > 200) {
                userBaseMsg.setGradeLevel(getSubstring(userBaseMsg.getGradeLevel(), 200));
            }

            if (StringUtils.isNotEmpty(userBaseMsg.getProfCerts()) && userBaseMsg.getProfCerts().length() > 200) {
                userBaseMsg.setProfCerts(getSubstring(userBaseMsg.getProfCerts(), 200));
            }
            if (StringUtils.isNotEmpty(userBaseMsg.getResidenceAddress()) &&
                userBaseMsg.getResidenceAddress().length() > 200) {
                userBaseMsg.setResidenceAddress(getSubstring(userBaseMsg.getResidenceAddress(), 200));
            }


        }
    }

    private String getSubstring(String str, int length) {
        if (str == null) {
            return null;
        }
        if (str.length() > length) {
            return str.substring(0, length);
        }
        return str;
    }

    private String getI18Msg(String param) {
        return i18nComponent.getI18nValue(param);
    }


    private UserExtMsgDTO readExcel(InputStream fileStream) {
        UserExtMsgDTO res = new UserExtMsgDTO();
        ExcelReader excelReader = EasyExcelFactory.read(fileStream).autoTrim(true).build();

        // 人才信息
        EasyExcelListener listener0 = getEasyExcelListener(excelReader, 0, 2);
        List<UserBaseMsgDTO> userBaseMsgList = readUserBase(listener0);
        res.setUserBaseMsgList(userBaseMsgList);

        // 内部任职履历
        EasyExcelListener listener1 = getEasyExcelListener(excelReader, 1, 2);
        List<UserCareerHisDTO> userCareerHisList = readUserCareer(listener1);
        res.setUserCareerHisList(userCareerHisList);
        // 奖罚信息
        EasyExcelListener listener2 = getEasyExcelListener(excelReader, 2, 2);
        List<UserRewardDTO> userRewardList = readReward(listener2);
        res.setUserRewardList(userRewardList);

        return res;
    }

    private List<UserRewardDTO> readReward(EasyExcelListener listener) {
        List<Map<Integer, String>> list = listener.getData();
        List<UserRewardDTO> importList = new ArrayList<>();
        for (Map<Integer, String> map : list) {
            int index = 0;
            UserRewardDTO result = new UserRewardDTO();
            result.setUsername(map.get(index++));
            result.setFullname(map.get(index++));
            result.setRpName(map.get(index++));
            result.setRpTypeStr(map.get(index++));
            result.setAcqTimeStr(map.get(index++));
            result.setPubFrom(map.get(index));
            if (StringUtils.isBlank(result.getUsername()) && StringUtils.isBlank(result.getFullname()) && StringUtils.isBlank(result.getRpName())
                && StringUtils.isBlank(result.getRpTypeStr()) && StringUtils.isBlank(result.getAcqTimeStr()) && StringUtils.isBlank(result.getPubFrom())) {
                continue;
            }
            importList.add(result);
        }
        return importList;

    }

    private EasyExcelListener getEasyExcelListener(ExcelReader excelReader, int sheetIndex, int rowNum) {
        ReadSheet sheet0 = excelReader.excelExecutor().sheetList().get(sheetIndex);
        EasyExcelListener listener0 = new EasyExcelListener();
        sheet0.setCustomReadListenerList(Collections.singletonList(listener0));
        sheet0.setHeadRowNumber(rowNum);
        sheet0.setAutoTrim(true);
        excelReader.read(sheet0);
        return listener0;
    }

    private List<UserCareerHisDTO> readUserCareer(EasyExcelListener listener) {
        List<Map<Integer, String>> list = listener.getData();
        List<UserCareerHisDTO> importList = new ArrayList<>();
        for (Map<Integer, String> map : list) {
            int index = 0;
            UserCareerHisDTO result = new UserCareerHisDTO();
            result.setUsername(map.get(index++));
            result.setFullname(map.get(index++));
            result.setThirdDeptName(map.get(index++));
            result.setThirdPositionName(map.get(index++));
            result.setThirdJobgradeName(map.get(index++));
            result.setActionName(map.get(index++));
            result.setOccurrenceTime(map.get(index));
            if (StringUtils.isBlank(result.getUsername()) && StringUtils.isBlank(result.getFullname()) && StringUtils.isBlank(result.getThirdDeptName())
                && StringUtils.isBlank(result.getThirdPositionName()) && StringUtils.isBlank(result.getThirdJobgradeName()) && StringUtils.isBlank(result.getActionName())
                && StringUtils.isBlank(result.getOccurrenceTime())) {
                continue;
            }
            importList.add(result);
        }
        return importList;
    }

    private List<UserBaseMsgDTO> readUserBase(EasyExcelListener listener) {
        List<Map<Integer, String>> list = listener.getData();
        List<UserBaseMsgDTO> importList = new ArrayList<>();
        for (Map<Integer, String> map : list) {
            int index = 0;
            UserBaseMsgDTO result = new UserBaseMsgDTO();
            result.setUsername(map.get(index++));
            result.setFullname(map.get(index++));
            result.setManagerStr(map.get(index++));
            result.setGradeLevel(map.get(index++));
            result.setProfCerts(map.get(index++));
            result.setResidenceAddress(map.get(index));
            if (StringUtils.isBlank(result.getUsername()) && StringUtils.isBlank(result.getFullname()) && StringUtils.isBlank(result.getManagerStr()) &&
                StringUtils.isBlank(result.getGradeLevel()) && StringUtils.isBlank(result.getProfCerts()) && StringUtils.isBlank(result.getResidenceAddress())) {
                continue;
            }
            importList.add(result);
        }
        return importList;


    }


    private InputStream getFileStream(MultipartFile file, UserExtImportCmd cmd) throws IOException {
        InputStream stream;
        if (Objects.nonNull(file)) {
            stream = file.getInputStream();
        } else {
            String downloadUrl = getDownloadUrl(cmd.getFileId());
            /*ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(ExcelUtil.getRemoteInputStream(downloadUrl));
            ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(
                    ExcelUtil.getRemoteInputStream(downloadUrl));
            String sheetName = excelReaderBuilder.build().excelExecutor().sheetList().get(0).getSheetName();
            if (!sheetName.equals(SkillGeneralEnum.GEN.getName())) {
                throw new ApiException(TalentsdExceptionKeys.SKILL_EXCEL_SHEET_NAME_ERROR);
            }
            */
            stream = ExcelUtil.getRemoteInputStream(downloadUrl);
        }
        return stream;
    }

    private String getDownloadUrl(String fileId) {
        return fileAclService.getDownloadUrl(fileId, ExceptionKeys.TRANSFER_IMPORT_FILE_ID_INVALID);
    }

}
