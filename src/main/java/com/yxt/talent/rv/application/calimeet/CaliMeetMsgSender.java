package com.yxt.talent.rv.application.calimeet;

import com.yxt.ApplicationQueryService;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetParticipantsMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet.MeetAttendeeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetAttendeePO;
import com.yxt.talent.rv.infrastructure.service.remote.CoreAclService;
import com.yxt.talent.rv.infrastructure.service.remote.MessageAclService;
import com.yxt.talent.rv.infrastructure.service.remote.dto.MessageDTO;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 校准会消息发送服务
 */
@Slf4j
@RequiredArgsConstructor
@ApplicationQueryService
public class CaliMeetMsgSender {

    /**
     * 加入校准会 消息模板
     **/
    private static final String TPL_CODE_CALIBRATION_JOIN = "calibrator_start";
    /**
     * 校准会开始 消息模板
     **/
    private static final String TPL_CODE_CALIBRATION_START = "calibration_start";

    /**
     * 校准会撤回 消息模板
     */
    private static final String TPL_CODE_CALIBRATION_WITHDRAW = "calibration_withdraw";

    /**
     * 校准会结束 消息模板
     */
    private static final String TPL_CODE_CALIBRATION_END = "calibration_end";

    /**
     * 移出校准会 消息模板
     */
    private static final String TPL_CODE_CALIBRATION_DEL = "calibrator_del";

    private static final String PLACEHOLDER_CALIBRATION_NAME = "{{calibrationName}}";
    private static final String PLACEHOLDER_CALIBRATION_START_TIME = "{{startTime}}";

    private static final String PLACEHOLDER_CALIBRATION_END_TIME = "{{endTime}}";
    private static final String SEND_USER_ID = "talentrv_calibration_auto_msg";

    private final MessageAclService messageAclService;
    private final AppProperties appProperties;
    private final CoreAclService coreAclService;
    private final CalimeetParticipantsMapper calimeetParticipantsMapper;


    /**
     * 发送模版消息 1-加人 2-开始 3-撤回 4-结束
     *
     * @param currentUser
     * @param cmList
     * @param msgType
     */
    @Async
    public void sendTemplateMessage(String token, UserCacheDetail currentUser, List<CalimeetPO> cmList, int msgType) {
        if (CollectionUtils.isEmpty(cmList)) {
            return;
        }

        for (CalimeetPO cm : cmList) {
            this.sendTemplateMessage(msgType, token, currentUser, cm);
        }
    }

    @Async
    public void sendTemplateMessagePointUserAsync(String token, UserCacheDetail currentUser, List<CalimeetPO> cmList,
            int msgType, List<String> msgUserIds) {
        if (CollectionUtils.isEmpty(cmList)) {
            return;
        }

        for (CalimeetPO cm : cmList) {
            this.sendTemplateMessagePointUser(msgType, token, currentUser, cm, msgUserIds);
        }
    }

    /**
     * 发送消息 msgType 1 = 校准会加人消息， 2 = 准会开始消息
     */
    public void sendTemplateMessagePointUser(int msgType, String token, UserCacheDetail currentUser,
            @Nullable CalimeetPO cm, List<String> msgUserIds) {

        if (cm == null) {
            return;
        }
        if (CollectionUtils.isEmpty(msgUserIds)) {
            return;
        }

        // 构造 messageBean
        MessageDTO messageDTO;
        if (msgType == 1) { // 加人消息
            messageDTO = getMessageBeanForCalibrationUser(cm.getId(), token, cm.getCalimeetName(), cm.getStartTime(),
                    msgUserIds, currentUser);
        } else if (msgType == 2) { // 开始消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(), msgUserIds, currentUser,
                    TPL_CODE_CALIBRATION_START);
        } else if (msgType == 3) { // 撤回消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(), msgUserIds, currentUser,
                    TPL_CODE_CALIBRATION_WITHDRAW);
        } else if (msgType == 4) { // 结束消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(), msgUserIds, currentUser,
                    TPL_CODE_CALIBRATION_END);
        } else if (msgType == 5) { // 移出消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(), msgUserIds, currentUser,
                    TPL_CODE_CALIBRATION_DEL);
        } else {
            return;
        }

        // 发送消息
        messageAclService.sendTemplateMessage(messageDTO);
    }

    /**
     * 发送消息 msgType 1 = 校准会加人消息， 2 = 准会开始消息
     */
    public void sendTemplateMessage(int msgType, String token, UserCacheDetail currentUser, @Nullable CalimeetPO cm) {

        if (cm == null) {
            return;
        }

        List<CalimeetParticipantsPO> meetingUsers = calimeetParticipantsMapper.selectByCaliMeetId(
                currentUser.getOrgId(), cm.getId());

        Set<String> allUserIds = meetingUsers.stream().map(CalimeetParticipantsPO::getUserId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(allUserIds)) {
            return;
        }

        //组织者
        Set<String> organizerIds = meetingUsers.stream().filter(e -> e.getUserType() == 1)
                .map(CalimeetParticipantsPO::getUserId).collect(Collectors.toSet());
        //参与者
        Set<String> userIds = meetingUsers.stream().filter(e -> e.getUserType() == 2)
                .map(CalimeetParticipantsPO::getUserId).collect(Collectors.toSet());

        // 构造 messageBean
        MessageDTO messageDTO;
        if (msgType == 1) { // 加人消息
            messageDTO = getMessageBeanForCalibrationUser(cm.getId(), token, cm.getCalimeetName(), cm.getStartTime(),
                    new ArrayList<>(userIds), currentUser);
        } else if (msgType == 2) { // 开始消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(),
                    new ArrayList<>(organizerIds), currentUser, TPL_CODE_CALIBRATION_START);
        } else if (msgType == 3) { // 撤回消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(),
                    new ArrayList<>(allUserIds), currentUser, TPL_CODE_CALIBRATION_WITHDRAW);
        } else if (msgType == 4) { // 结束消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(),
                    new ArrayList<>(organizerIds), currentUser, TPL_CODE_CALIBRATION_END);
        } else if (msgType == 5) { // 移出消息
            messageDTO = getMessageBeanForCalibration(cm.getId(), token, cm.getCalimeetName(), new ArrayList<>(userIds),
                    currentUser, TPL_CODE_CALIBRATION_DEL);
        } else {
            return;
        }

        // 发送消息
        messageAclService.sendTemplateMessage(messageDTO);
    }

    private MessageDTO getMessageBeanForCalibrationUser(String id, String token, String meetingName,
            LocalDateTime startTime, List<String> userIds, UserCacheDetail currentUser) {
        MessageDTO mb = getMessageBeanForCalibration(id, token, meetingName, userIds, currentUser,
                TPL_CODE_CALIBRATION_JOIN);
        Map<String, String> placeholderMap = mb.getPlaceholderMap();
        String startValue = DateTimeUtil.dateToString(startTime, DateTimeUtil.YYYY_MM_DD_HH_MM);
        placeholderMap.put(PLACEHOLDER_CALIBRATION_START_TIME, startValue);
        String endValue = DateTimeUtil.dateToString(startTime, DateTimeUtil.YYYY_MM_DD_HH_MM);
        placeholderMap.put(PLACEHOLDER_CALIBRATION_END_TIME, endValue);
        return mb;
    }

    private MessageDTO getMessageBeanForCalibration(String id, String token, String meetingName, List<String> userIds,
            UserCacheDetail currentUser, String tplCode) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTemplateCode(tplCode);
        messageDTO.setId(id);
        messageDTO.setOrgId(currentUser.getOrgId());
        messageDTO.setUserId(currentUser.getUserId());
        messageDTO.setUserFullName(currentUser.getFullname());
        messageDTO.setDomain(currentUser.getDomain());
        messageDTO.setToken(token);
        messageDTO.setUserIds(userIds);

        HashMap<String, String> placeholderMap = new HashMap<>(8);

        String url = coreAclService.getScanentryURL(currentUser.getOrgId(), appProperties.getCaliMeetMsgUrl(), "");

        placeholderMap.put("{{url}}", url);
        placeholderMap.put(PLACEHOLDER_CALIBRATION_NAME, meetingName);
        messageDTO.setPlaceholderMap(placeholderMap);

        return messageDTO;
    }
}
