package com.yxt.talent.rv.application.perf.lagecy;

import com.yxt.common.component.SpringContextHolder;
import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodCmd;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodSortCmd;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodIdNameVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.NumberEnum;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PerfPeriodAppService {

    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfMapper perfMapper;
    private final ActivityPerfMapper activityPerfMapper;

    public static PerfPeriodAppService self() {
        return SpringContextHolder.getBean(PerfPeriodAppService.class);
    }

    /**
     * 查询机构下的绩效周期
     *
     * @param orgId 机构id
     */
    public List<PerfPeriodVO> findList(String orgId) {
        List<PerfPeriodVO> result = new ArrayList<>();
        List<PerfPeriodPO> list = perfPeriodMapper.selectByOrgId(orgId);
        if (CollectionUtils.isNotEmpty(list)) {
            result = BeanCopierUtil.convertList(list, PerfPeriodPO.class, PerfPeriodVO.class);
        }
        // 盘点绩效周期是否被使用
        // rv_performance 使用绩效
        List<String> periodIds = perfMapper.selectPeriodIds(orgId);
        // 绩效评估活动中使用的绩效周期
        Set<String> periodIdsInActv = new HashSet<>();
        activityPerfMapper.findAllPeriodIds(orgId).forEach(r -> periodIdsInActv.addAll(List.of(r.split(";"))));

        for (PerfPeriodVO perfPeriodVO : result) {
            perfPeriodVO.setEditable(1);
            // 判定绩效周期是否被盘点使用的逻辑中，需剔除已删除的盘点项目和绩效评估活动
            if (periodIds.contains(perfPeriodVO.getId()) || periodIdsInActv.contains(perfPeriodVO.getId())) {
                perfPeriodVO.setUsed(1);
                // 当绩效周期中绩效类型、绩效年份都为空时，支持编辑绩效周期，无论是否被盘点项目使用过
                if (!(perfPeriodVO.getCycle() == null && perfPeriodVO.getYearly() == null)) {
                    perfPeriodVO.setEditable(0);
                }
            }

            // 如果是年度绩效，需要把period字段置空(否则会在前端渲染出来)
            if (Objects.equals(3, perfPeriodVO.getCycle())) {
                perfPeriodVO.setPeriod(null);
            }
        }

        return result;
    }

    /**
     * 新增绩效周期
     *
     * @param orgId         机构id
     * @param userId        操作人id
     * @param perfPeriodCmd
     */
    public PerfPeriodIdNameVO addPerfPeriod(String orgId, String userId, PerfPeriodCmd perfPeriodCmd) {
        PerfPeriodIdNameVO result = new PerfPeriodIdNameVO();
        // 新增数量校验，最多50个
        /*long count = perfPeriodMapper.countByOrgId(orgId);
        Validate.isTrue(count <= 50, ExceptionKeys.PERF_PERIOD_LIMIT);*/
        // 新增重名校验
        long check = perfPeriodMapper.selectByOrgIdAndPeriodNameAndIdNeIfHas(orgId, perfPeriodCmd.getPeriodName(), "");
        Validate.isTrue(check == 0, ExceptionKeys.PERF_PERIOD_NAME_CONFLICT);
        // 校验 绩效类型 + 绩效年份 + 绩效周期组合唯一
        chkKeyUnique(perfPeriodCmd, orgId, StringUtils.EMPTY);

        PerfPeriodPO perfPeriod = new PerfPeriodPO();
        perfPeriod.setId(ApiUtil.getUuid());
        perfPeriod.setPeriodName(perfPeriodCmd.getPeriodName());
        EntityUtil.setAuditFields(perfPeriod, userId);
        // 处理排序
        int currentMaxSort = perfPeriodMapper.currentMaxSort(orgId);
        // 获取当前机构下绩效周期最大数量，新增的绩效周期排序+1
        perfPeriod.setOrderIndex(currentMaxSort + 1);
        perfPeriod.setOrgId(orgId);
        perfPeriod.setCycle(perfPeriodCmd.getCycle());
        perfPeriod.setYearly(perfPeriodCmd.getYearly());
        perfPeriod.setPeriod(getPeriod(perfPeriodCmd));
        if (Objects.nonNull(perfPeriodCmd.getScoreTotal())) {
            perfPeriod.setScoreTotal(BigDecimal.valueOf(perfPeriodCmd.getScoreTotal()));
        }
        perfPeriod.setDeleted(NumberEnum.ZERO.getNumber());
        perfPeriodMapper.insertOrUpdate(perfPeriod);
        result.setId(perfPeriod.getId());
        result.setName(perfPeriod.getPeriodName());
        return result;
    }

    private static Integer getPeriod(PerfPeriodCmd perfPeriodCmd) {
        return perfPeriodCmd.getCycle() != null && perfPeriodCmd.getCycle() == 3 && perfPeriodCmd.getPeriod() == null ? perfPeriodCmd.getYearly() : perfPeriodCmd.getPeriod();
    }

    private void chkKeyUnique(PerfPeriodCmd perfPeriodCmd, String orgId, String id) {
        List<PerfPeriodPO> perfPeriodList = perfPeriodMapper.selectByOrgId(orgId);
        if (perfPeriodCmd.getPeriod() == null && perfPeriodCmd.getYearly() == null
                && perfPeriodCmd.getCycle() == null) {
            return;
        }
        List<String> keys = new ArrayList<>();
        for (PerfPeriodPO perfPeriodPO : perfPeriodList) {
            if (StringUtils.isNotBlank(id) && perfPeriodPO.getId().equals(id)) {
                continue;
            }
            if (perfPeriodPO.getPeriod() == null && perfPeriodPO.getYearly() == null
                    && perfPeriodPO.getCycle() == null) {
                continue;
            }
            String sb = (perfPeriodPO.getPeriod() == null ? -1 : perfPeriodPO.getPeriod()) + "+" + (
                    perfPeriodPO.getYearly() == null ? -1 : perfPeriodPO.getYearly()) + "+" + (
                    perfPeriodPO.getCycle() == null ? -1 : perfPeriodPO.getCycle());
            keys.add(sb);
        }

        StringBuilder param = new StringBuilder();
        param.append(perfPeriodCmd.getPeriod() == null ? -1 : perfPeriodCmd.getPeriod()).append("+")
                .append(perfPeriodCmd.getYearly() == null ? -1 : perfPeriodCmd.getYearly()).append("+")
                .append(perfPeriodCmd.getCycle() == null ? -1 : perfPeriodCmd.getCycle());

        if (keys.contains(param.toString())) {
            throw new ApiException(ExceptionKeys.PERF_PROJECT_KEY_EXIST);
        }

    }

    /**
     * 编辑绩效周期
     *
     * @param orgId         机构id
     * @param userId        操作人id
     * @param perfPeriodCmd
     */
    public void editPerfPeriod(String orgId, String userId, PerfPeriodCmd perfPeriodCmd) {
        PerfPeriodPO perfPeriod = perfPeriodMapper.selectByOrgIdAndId(orgId, perfPeriodCmd.getId());
        if (perfPeriod == null) {
            throw new ApiException(ExceptionKeys.PERF_PERIOD_NOT_EXIST);
        }
        // 新增重名校验
        long check = perfPeriodMapper.selectByOrgIdAndPeriodNameAndIdNeIfHas(orgId, perfPeriodCmd.getPeriodName(),
                                                                             perfPeriodCmd.getId());
        Validate.isTrue(check == 0, ExceptionKeys.PERF_PERIOD_NAME_CONFLICT);
        // 校验绩效周期是否已经被使用
        //chkPeriodUsed(perfPeriodCmd.getId(), orgId);
        // 检查 绩效类型 + 绩效年份 + 绩效周期组合唯一
        chkKeyUnique(perfPeriodCmd, orgId, perfPeriodCmd.getId());

        BeanCopierUtil.copy(perfPeriodCmd, perfPeriod, new String[]{"scoreTotal"}, false);
        perfPeriod.setScoreTotal(perfPeriodCmd.getScoreTotal() == null ? null : BigDecimal.valueOf(perfPeriodCmd.getScoreTotal()));
        EntityUtil.setUpdate(perfPeriod, userId);
        perfPeriod.setPeriod(getPeriod(perfPeriodCmd));
        perfPeriodMapper.insertOrUpdate(perfPeriod);
    }

    /**
     * 绩效周期拖拽排序方法
     *
     * @param orgId      机构id
     * @param userId     操作人id
     * @param periodSort
     */
    public void sort(String orgId, String userId, PerfPeriodSortCmd periodSort) {
        // 查询变更的绩效周期数据
        PerfPeriodPO perfPeriod = perfPeriodMapper.selectByOrgIdAndId(orgId, periodSort.getId());
        if (perfPeriod == null) {
            throw new ApiException(ExceptionKeys.PERF_PERIOD_NOT_EXIST);
        }
        List<PerfPeriodPO> periodList = perfPeriodMapper.selectByOrgId(orgId);
        int oldSort = getOldSort(periodSort, periodList);
        int newSort = periodSort.getSort();
        // 判断新旧两个排序之间的差异，如果新旧排序无差异，则不做任何处理
        if (oldSort == newSort) {
            return;
        }
        // 获取两个数据差位之间所有的数据进行重新排序
        int move = getMove(oldSort, newSort);
        int index = 1;
        List<PerfPeriodPO> updateList = new ArrayList<>();
        getUpdateList(userId, perfPeriod, periodList, newSort, move, index, updateList);
        if (CollectionUtils.isNotEmpty(updateList)) {
            perfPeriodMapper.insertOrUpdateBatch(updateList);
        }
    }

    private void getUpdateList(String userId, PerfPeriodPO perfPeriod, List<PerfPeriodPO> periodList, int newSort,
                               int move, int index, List<PerfPeriodPO> updateList) {
        int targetIndex;
        for (PerfPeriodPO perfPeriodPO : periodList) {
            // 向下移动新顺序后的所有 数据库排序向下移动
            if (move == -1) {
                if (index == newSort) {
                    targetIndex = perfPeriodPO.getOrderIndex();
                    // 移动到此位置
                    perfPeriod.setOrderIndex(targetIndex + 1);
                    EntityUtil.setUpdate(perfPeriod, userId);
                    updateList.add(perfPeriod);
                } else if (index > newSort) {
                    perfPeriodPO.setOrderIndex(perfPeriodPO.getOrderIndex() + 1);
                    EntityUtil.setUpdate(perfPeriodPO, userId);
                    updateList.add(perfPeriodPO);
                }
            } else {
                if (perfPeriodPO.getId().equals(perfPeriod.getId())) {
                    break;
                }
                // 向上移动，把newSort 和oldSort中间的顺序改变
                setUpdateList(userId, perfPeriod, newSort, index, updateList, perfPeriodPO);
            }
            index++;
        }
    }

    private void setUpdateList(String userId, PerfPeriodPO perfPeriod, int newSort, int index,
                               List<PerfPeriodPO> updateList, PerfPeriodPO perfPeriodPO) {
        int targetIndex;
        if (index == newSort) {
            targetIndex = perfPeriodPO.getOrderIndex();
            perfPeriod.setOrderIndex(targetIndex);
            EntityUtil.setUpdate(perfPeriod, userId);
            updateList.add(perfPeriod);

            // 当前位置旧数据排序 +1
            perfPeriodPO.setOrderIndex(targetIndex + 1);
            EntityUtil.setUpdate(perfPeriodPO, userId);
            updateList.add(perfPeriodPO);
        } else if (index > newSort) {
            perfPeriodPO.setOrderIndex(perfPeriodPO.getOrderIndex() + 1);
            EntityUtil.setUpdate(perfPeriodPO, userId);
            updateList.add(perfPeriodPO);
        }
    }

    private int getOldSort(PerfPeriodSortCmd periodSort, List<PerfPeriodPO> periodList) {
        int oldSort = -1;
        int temp = 1;
        for (PerfPeriodPO perfPeriodPO : periodList) {
            if (periodSort.getId().equals(perfPeriodPO.getId())) {
                oldSort = temp;
            }
            temp++;
        }
        return oldSort;
    }

    private int getMove(int oldSort, int newSort) {
        int move;
        if (oldSort > newSort) {
            // 向上移动
            move = 1;
        } else {
            // 向下移动
            move = -1;
        }
        return move;
    }

    /**
     * 根据id删除绩效周期（物理删除）
     *
     * @param orgId 机构id
     * @param id    绩效周期id
     */
    public void removePeriod(String orgId, String id) {
        // 查询此绩效周期是否存在关联数据，如果存在关联数据无法执行删除
        List<PerfPO> list = perfMapper.selectByOrgIdAndPeriodId(orgId, id);
        Validate.isTrue(CollectionUtils.isEmpty(list), ExceptionKeys.PERF_PERIOD_HAS_DATA);
        // 是否被盘点项目引用
        Set<String> periodIdsInActv = new HashSet<>();
        activityPerfMapper.findAllPeriodIds(orgId).forEach(r -> periodIdsInActv.addAll(List.of(r.split(";"))));

        if (periodIdsInActv.contains(id)) {
            throw new ApiException(ExceptionKeys.PERF_PROJECT_USED);
        }
        PerfPeriodPO perfPeriod = perfPeriodMapper.selectByOrgIdAndId(orgId, id);
        if (perfPeriod != null) {
            EntityUtil.delete(perfPeriod).ifPresent(perfPeriodMapper::insertOrUpdate);
        }
    }

    public List<PerfPeriodPO> listByIds(String orgId, List<String> periodIds) {
        return perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
    }
}
