package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CaliUpdateUserResultReq {
    //维度结果校准 sdDimId + gridLevelId/scoreValue/qualifiedPtg
    @Schema(description = "人才标准的维度id")
    private String sdDimId;
    @Schema(description = "宫格分层id, rv_xpd_grid_level.id", hidden = true)
    private String gridLevelId;
    @Schema(description = "分值, 包括绩效得分", hidden = true)
    private BigDecimal scoreValue;
    @Schema(description = "达标率", hidden = true)
    private BigDecimal qualifiedPtg;
    //指标结果校准,sdIndicatorId + scoreValue/qualified
    @Schema(description = "人才标准的维度id")
    private String sdIndicatorId;
    @Schema(description = "是否达标", hidden = true)
    private Integer qualified;
    @Schema(description = "绩效活动等级id", hidden = true)
    private String perfLevelId;
    @Schema(description = "校准结果")
    private String caliVal;
}
