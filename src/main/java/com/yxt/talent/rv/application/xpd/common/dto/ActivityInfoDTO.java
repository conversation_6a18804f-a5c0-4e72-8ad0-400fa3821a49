package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/10 18:13
 */
@Data
@Schema(description = "活动基本信息")
public class ActivityInfoDTO {

    @Schema(description = "活动类型 0:普通[项目中的活动] 1:导入数据 2:个人档案")
    private Integer actvType;

    @Schema(description = "活动ID 如果活动类型是 导入数据 或 个人档案，此项为空")
    private String actvId;

    @Schema(description = "活动名称")
    private String actvName;

    @Schema(description = "活动创建时间")
    private Date createTime;

    @Schema(description = "评估方式,1:按等级，2：按分数")
    private Integer evalType;

    @Schema(description = "指标在该活动中的总分")
    private BigDecimal indicatorTotalScore;
}
