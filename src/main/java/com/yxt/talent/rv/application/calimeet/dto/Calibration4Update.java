package com.yxt.talent.rv.application.calimeet.dto;

import java.lang.String;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;

import java.util.List;
import java.lang.Integer;
import java.lang.Long;
import java.util.Map;

import com.google.common.collect.Maps;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

@Getter
@Setter
@Schema(description = "校准任务更新请求")
public class Calibration4Update {

    @Schema(description = "校准任务名称;名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = ExceptionKeys.CALI_MEET_PARAM_NAME_NOT_BLANK)
    private String name;
    @Schema(description = "组织形式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = ExceptionKeys.CALI_MEET_PARAM_TYPE_NOT_BLANK)
    private String meetingType;
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date startTime;
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date endTime;
    @Schema(description = "组织人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = ExceptionKeys.CALI_MEET_PARAM_ORGUSERS_NOT_EMPTY)
    private List<AmSlDrawer4ReqDTO> organizator;
    @Schema(description = "校准人", requiredMode = Schema.RequiredMode.REQUIRED)
    //    @NotEmpty(message = ExceptionKeys.CALI_MEET_PARAM_CALIUSERS_NOT_EMPTY)
    private List<AmSlDrawer4ReqDTO> calibrator;
    @Schema(description = "校准方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = ExceptionKeys.CALI_MEET_PARAM_CALITYPE_NOTNULL)
    private String calibType;
    @Schema(description = "校准比例控制")
    @NotNull(message = ExceptionKeys.CALI_MEET_PARAM_RATECTRL_NOTNULL)
    private Integer rateControl;
    @Schema(description = "状态")
    private String meetStatus;
    @Schema(description = "校准人数")
    private Long calibUsers;
    @Schema(description = "会议记录")
    private String meetingRecord;

    @Schema(description = "扩展字段")
    private Map<String, Object> _spares = Maps.newHashMap();
}
