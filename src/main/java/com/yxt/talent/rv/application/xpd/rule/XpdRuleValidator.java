package com.yxt.talent.rv.application.xpd.rule;

import com.alibaba.fastjson2.JSON;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdk.common.bean.RuleMainBase;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.talent.rv.application.xpd.common.CommonValidator;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.XpdImportTypeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.*;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 项目规则校验器
 *
 * <AUTHOR>
 * @date 2024/12/18 18:34
 */
@Component
@RequiredArgsConstructor
public class XpdRuleValidator {

    private final XpdDimMapper xpdDimMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final XpdRuleCalcIndicatorMapper xpdRuleCalcIndicatorMapper;
    private final XpdImportMapper xpdImportMapper;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final SpsdAclService spsdAclService;
    private final SpRuleService spRuleService;
    private final XpdDimRuleValidator xpdDimRuleValidator;

    private static final BigDecimal HUNDRED = new BigDecimal(100);

    /**
     * 校验结果类型
     * 计算方式选择按维度结果计算时，可选按维度分层结果、全局规则中配置的结果类型，默认选中按维度分层结果
     * 计算方式选择按指标结果计算时，仅展示选中全局规则中配置的结果类型
     * 如果存在直接导入维度结果的盘点维度，则只允许选择按维度分层结果
     *
     * @return error
     */
    @Nullable
    public ErrorInfo validateResultType(String orgId, Integer xpdResultType, Integer confResultType, Integer calcType, List<XpdImportPO> importDims) {

        // 如果存在直接导入维度结果的盘点维度，则只允许选择按维度分层结果
        int importCnt = importDims.size();
        if (importCnt > 0 && !XpdResultTypeEnum.byDimLevelResult(xpdResultType)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_RESULTTYPE_ERROR_1, "存在导入维度结果的盘点维度，结果类型只能选择维度分层结果");
        }

        // 计算方式选择按指标结果计算时，仅能选中全局规则中配置的结果类型
        if (XpdCalcTypeEnum.byIndicator(calcType)) {
            boolean equals = resultTypeEquals(confResultType, xpdResultType);
            if (!equals) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_RESULTTYPE_ERROR_2, "结果类型不正确，只能选择全局规则中配置的结果类型");
            }
        } else {
            // 计算方式选择按维度结果计算时，可选按维度分层结果、全局规则中配置的结果类型
            if (!XpdResultTypeEnum.byDimLevelResult(xpdResultType)) {
                boolean equals = resultTypeEquals(confResultType, xpdResultType);
                if (!equals) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_RESULTTYPE_ERROR_3, "结果类型不正确，只能选择维度分层结果和全局规则中配置的结果类型");
                }
            }
        }

        return null;
    }

    private static boolean resultTypeEquals(Integer confResultType, Integer resultType) {

        if (XpdConfResultTypeEnum.byScore(confResultType) && XpdResultTypeEnum.byScore(resultType)) {
            return true;
        }
        return XpdConfResultTypeEnum.byRate(confResultType) && XpdResultTypeEnum.byRatio(resultType);
    }

    /**
     * 校验计算规则 && 分层规则
     *
     * @param totalScore 项目总分
     * @return ErrorInfo
     */
    @Nullable
    public ErrorInfo validateCalcAndLevelRule(String orgId,
                                              XpdPO xpd,
                                              XpdRulePO xpdRule,
                                              List<XpdRuleCalcDto> ruleCalcList,
                                              List<XpdLevelRuleDto> levelRuleList,
                                              BigDecimal totalScore) {

        if (CollectionUtils.isEmpty(levelRuleList)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_EMPTY, "分层规则未配置");
        }

        // 计算方式：按指标结果计算
        if (XpdCalcTypeEnum.byIndicator(xpdRule.getCalcType())) {
            return validateCalcAndLevelRuleByIndicatorWhenUp(orgId, xpd, xpdRule, ruleCalcList, levelRuleList, totalScore);
        }

        // 计算方式：按维度结果计算
        return validateCalcAndLevelRuleByDimWhenUp(xpdRule, ruleCalcList, levelRuleList, totalScore);
    }

    /**
     * 校验
     * 项目计算方式为<按指标结果计算>
     * 1.校验计算规则
     * 2.校验分层规则
     *
     * @param orgId   机构ID
     * @param xpdRule 项目规则
     * @return ErrorInfo 错误信息，如果没有错误，返回null
     */
    @Nullable
    public ErrorInfo validateByIndicator(String orgId, XpdPO xpd, XpdRulePO xpdRule, Map<String, BigDecimal> totalScoreMap) {
        String xpdRuleId = xpdRule.getId();

        // 结果类型：指标得分
        if (XpdResultTypeEnum.byScore(xpdRule.getResultType())) {
            // 计算规则：高级公式
            if (XpdCalcRuleEnum.isFormula(xpdRule.getCalcRule())) {
                // 校验高级公式
                return validateFormula(orgId, xpd, xpdRule.getFormula());
            } else {
                // 计算规则：快捷配置
                // 校验计算规则
                List<XpdRuleCalcIndicatorPO> ruleCalcList = xpdRuleCalcIndicatorMapper.listByXpdRuleId(orgId, xpdRuleId);
                ErrorInfo errorInfo = validateRefIds(orgId, xpd, ruleCalcList);
                if (Objects.nonNull(errorInfo)) {
                    return errorInfo;
                }
            }

            // 校验分层规则
            // 指标得分 && 固定值
            BigDecimal totalScore = null;
            if (XpdLevelTypeEnum.byFixedValue(xpdRule.getLevelType())) {
                totalScore = totalScoreMap.get(xpd.getId());
            }
            List<XpdLevelPO> xpdLevels = xpdLevelMapper.listByXpdRuleId(orgId, xpdRuleId);
            return validateLevelRule(xpdRule, xpdLevels, totalScore);
        } else if (XpdResultTypeEnum.byRatio(xpdRule.getResultType())) {
            // 结果类型：指标达标率
            // 校验计算规则
            List<XpdRuleCalcIndicatorPO> ruleCalcList = xpdRuleCalcIndicatorMapper.listByXpdRuleId(orgId, xpdRuleId);
            return validateRefIds(orgId, xpd, ruleCalcList);
        }
        return null;
    }

    /**
     * 校验
     * 项目计算方式为<按维度结果计算>
     * 1.校验结果类型
     * 2.校验计算规则
     * 3.校验分层规则
     * 项目计算规则中，使用了错误的维度，提示“计算维度中存在仅导入结果的维度，请调整”
     * 场景：项目规则配置为使用维度得分/达标率计算后，将其中某个维度改为导入维度结果，导致无法获取其得分/达标率，从而影响盘点项目结果计算
     *
     * @param orgId         机构ID
     * @param xpdRule       项目规则
     * @param totalScoreMap 用于分层规则总分校验
     * @return ErrorInfo 错误信息，如果没有错误，返回null
     */
    @Nullable
    public ErrorInfo validateByDim(String orgId, Integer confResultType, XpdRulePO xpdRule, List<XpdDimRulePO> dimRuleList, Map<String, BigDecimal> totalScoreMap) {

        List<XpdImportPO> importDims = xpdImportMapper.findByXpdIdAndImportType(orgId, xpdRule.getXpdId(), XpdImportTypeEnum.DIM.getCode(), true);
        Map<String, XpdImportPO> importDimMap = StreamUtil.list2map(importDims, XpdImportPO::getSdDimId);
        // 校验结果类型
        ErrorInfo errorInfo = validateResultType(orgId, xpdRule.getResultType(), confResultType, xpdRule.getCalcType(), importDims);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }

        // 校验计算规则
        // 结果类型：非维度分层结果
        if (!XpdResultTypeEnum.byDimLevelResult(xpdRule.getResultType())) {
            for (XpdDimRulePO dimRule : dimRuleList) {
                if (importDimMap.containsKey(dimRule.getSdDimId())) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_DIM_IMPORT_ERROR, "计算维度中存在仅导入结果的维度，请调整");
                }
            }
        }

        // 校验分层规则
        List<XpdLevelPO> xpdLevels = xpdLevelMapper.listByXpdRuleId(orgId, xpdRule.getId());
        BigDecimal maxLevelValue = null;
        // 维度得分 && 按固定值
        if (XpdResultTypeEnum.byScore(xpdRule.getResultType()) && XpdLevelTypeEnum.byFixedValue(xpdRule.getLevelType())) {
            maxLevelValue = totalScoreMap.getOrDefault(xpdRule.getXpdId(), BigDecimal.ZERO);
        }
        return validateLevelRule(xpdRule, xpdLevels, maxLevelValue);
    }

    /**
     * 编辑项目规则时校验
     * 项目计算方式为<按指标结果计算>
     * 1.校验指标的合法性
     * 2.校验数据来源的合法性
     * 3.校验计算逻辑的合法性
     * 4.校验权重之和
     *
     * @param orgId      机构ID
     * @param xpdRule    项目规则
     * @param totalScore 项目总分
     * @return ErrorInfo 错误信息，如果没有错误，返回null
     */
    @Nullable
    public ErrorInfo validateCalcAndLevelRuleByIndicatorWhenUp(String orgId,
                                                               XpdPO xpd,
                                                               XpdRulePO xpdRule,
                                                               List<XpdRuleCalcDto> ruleCalcList,
                                                               List<XpdLevelRuleDto> levelRuleList,
                                                               BigDecimal totalScore) {

        List<String> lastIndicatorIds = StreamUtil.mapList(spsdAclService.getLastIndicators(orgId, xpd.getModelId()), IndicatorDto::getItemId);

        // 结果类型：得分
        if (XpdResultTypeEnum.byScore(xpdRule.getResultType())) {
            // 计算规则：高级公式
            if (XpdCalcRuleEnum.isFormula(xpdRule.getCalcRule())) {
                // 校验高级公式
                return CommonValidator.checkXpdFormula(xpdRule.getFormula());
            }

            // 计算规则：快捷配置
            if (CollectionUtils.isEmpty(ruleCalcList)) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY, "计算规则未配置");
            }

            ErrorInfo errorInfo = validateIndicatorsWhenUp(orgId, xpd, xpdRule, ruleCalcList, lastIndicatorIds);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }

            // 校验分层规则
            // 项目总分
            errorInfo = validateLevelRule(xpdRule, convert2XpdLevelPo(orgId, xpdRule.getXpdId(), levelRuleList), totalScore);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
        } else if (XpdResultTypeEnum.byRatio(xpdRule.getResultType())) {
            // 结果类型：达标率
            for (XpdRuleCalcDto indicator : ruleCalcList) {
                // 校验维度的数据
                ErrorInfo errorInfo = validateIndicatorWhenUp(orgId, lastIndicatorIds, xpdRule.getResultType(), xpd, indicator);
                if (Objects.nonNull(errorInfo)) {
                    return errorInfo;
                }
            }

            // 校验分层规则
            return validateLevelRule(xpdRule, convert2XpdLevelPo(orgId, xpdRule.getXpdId(), levelRuleList), null);
        }
        return null;
    }

    /**
     * 校验指标数据的基本信息
     */
    @Nullable
    private ErrorInfo validateIndicatorsWhenUp(String orgId,
                                               XpdPO xpd,
                                               XpdRulePO xpdRule,
                                               List<XpdRuleCalcDto> ruleCalcList,
                                               List<String> lastIndicatorIds) {
        BigDecimal sum = BigDecimal.ZERO;
        Set<String> sdIds = new HashSet<>();
        for (XpdRuleCalcDto indicator : ruleCalcList) {
            ErrorInfo errorInfo = validateIndicatorWhenUp(orgId, lastIndicatorIds, xpdRule.getResultType(), xpd, indicator);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
            if (Objects.isNull(indicator.getWeight())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_DIM_WEIGHT_EMPTY, "权重不能为空");
            }
            sum = sum.add(indicator.getWeight());

            // 校验重复性
            if (sdIds.contains(indicator.getSdDtos().get(0).getSdId())) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_INDICATOR_DUPLICATE, "计算规则中不能重复选择指标");
            }
            sdIds.add(indicator.getSdDtos().get(0).getSdId());
        }
        // 校验权重
        if (HUNDRED.compareTo(sum) != 0) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_DIM_WEIGHT_SUM_ERROR, "权重之和必须为100%");
        }

        return null;
    }

    private boolean validateIndicatorCalcMethod(Integer resultType, Integer calcMethod) {
        return (XpdResultTypeEnum.byScore(resultType) && DimCalcMethodEnum.byScore(calcMethod))
                || (XpdResultTypeEnum.byRatio(resultType) && DimCalcMethodEnum.byRatio(calcMethod));
    }

    @Nullable
    private ErrorInfo validateIndicatorWhenUp(String orgId, List<String> lastIndicatorIds, Integer xpdResultType, XpdPO xpd, XpdRuleCalcDto indicator) {
        // 校验指标的合法性
        // 校验指标
        if (CollectionUtils.isEmpty(indicator.getSdDtos())) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_EMPTY, "指标为空，请检查");
        }
        String sdId = indicator.getSdDtos().get(0).getSdId();
        if (!lastIndicatorIds.contains(sdId)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_NOTEXIST, "指标不存在，请检查");
        }
        // 校验数据来源
        //        if (CollectionUtils.isEmpty(indicator.getRefIds())) {
        //            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_REF_EMPTY, "指标数据来源为空，请检查");
        //        }
        ErrorInfo errorInfo = xpdDimRuleValidator.checkRefIds(orgId, sdId, xpd, indicator.getRefIds());
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }
        // 校验计算逻辑
        if (!validateIndicatorCalcMethod(xpdResultType, indicator.getCalcMethod())) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_INDICATOR_CALMETHOD_ERROR, "计算逻辑不正确，请检查");
        }
        return null;
    }

    /**
     * 校验计算规则-编辑项目
     * 项目计算方式为<按维度结果计算>
     * 1.校验维度的合法性
     * 2.校验权重之和
     * 3.校验分层规则
     *
     * @param xpdRule      项目规则
     * @param ruleCalcList 计算规则
     * @return ErrorInfo 错误信息，如果没有错误，返回null
     */
    @Nullable
    public ErrorInfo validateCalcAndLevelRuleByDimWhenUp(XpdRulePO xpdRule,
                                                         List<XpdRuleCalcDto> ruleCalcList,
                                                         List<XpdLevelRuleDto> levelRuleList,
                                                         BigDecimal totalScore) {
        List<String> sdDimIds = StreamUtil.mapList(xpdDimMapper.listByXpdId(xpdRule.getOrgId(), xpdRule.getXpdId()), XpdDimPO::getSdDimId);
        Set<String> sdIds = new HashSet<>();

        // 校验权重之和
        // 结果类型：维度分层结果
        if (XpdResultTypeEnum.byDimLevelResult(xpdRule.getResultType())) {
            // 校验分层规则
            return validateLevelRule(xpdRule, convert2XpdLevelPo(xpdRule.getOrgId(), xpdRule.getXpdId(), levelRuleList), null);
        } else if (XpdResultTypeEnum.byScore(xpdRule.getResultType())) {
            // 结果类型：得分
            if (CollectionUtils.isEmpty(ruleCalcList)) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY, "计算规则未配置");
            }

            // 校验维度的合法性
            for (XpdRuleCalcDto ruleCalcDto : ruleCalcList) {
                String sdId = ruleCalcDto.getSdDtos().get(0).getSdId();
                if (sdIds.contains(sdId)) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_DIM_DUPLICATE, "计算规则中不能重复选择维度");
                }
                if (!sdDimIds.contains(sdId)) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_CALC_DIM_NOTEXIST, "计算规则中的维度不存在");
                }
                sdIds.add(sdId);
            }

            ErrorInfo errorInfo = CommonValidator.sumPercent(ruleCalcList, "weight", ExceptionKeys.XPD_RULE_CALC_DIM_WEIGHT_SUM_ERROR, "权重之和必须为100%");
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }

            // 校验分层规则
            errorInfo = validateLevelRule(xpdRule, convert2XpdLevelPo(xpdRule.getOrgId(), xpdRule.getXpdId(), levelRuleList), totalScore);
            if (Objects.nonNull(errorInfo)) {
                return errorInfo;
            }
        } else if (XpdResultTypeEnum.byRatio(xpdRule.getResultType())) {
            // 结果类型：达标率
            // 校验分层规则
            return validateLevelRule(xpdRule, convert2XpdLevelPo(xpdRule.getOrgId(), xpdRule.getXpdId(), levelRuleList), null);
        }
        return null;
    }

    /**
     * 校验项目规则的分层规则
     * 1.按比例：人员占比必须为100%
     * 2.按固定值
     * 从上到下必须依次递减，若不是则有问题的输入框高亮并提示“分值/达标率不可超过上级分层设置的分值”
     * 最低分层分值必须为0
     *
     * @param levelRuleList 分层列表
     * @return ErrorInfo
     */
    @Nullable
    public ErrorInfo validateLevelRule(XpdRulePO xpdRule, List<XpdLevelPO> levelRuleList, BigDecimal maxValue) {

        if (CollectionUtils.isEmpty(levelRuleList)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_EMPTY, "分层规则未配置");
        }

        // 按维度分层校验
        if (XpdResultTypeEnum.byDimLevelResult(xpdRule.getResultType())) {
            // 校验分层
            RuleMainBase mainBase = new RuleMainBase();
            mainBase.setBizId(xpdRule.getXpdId());
            mainBase.setOrgId(xpdRule.getOrgId());
            for (XpdLevelPO level : levelRuleList) {
                try {
                    SpRuleBean spRuleBean = BeanHelper.json2Bean(level.getFormula(), SpRuleBean.class);
                    boolean right = spRuleService.deepCheckRule(mainBase, spRuleBean);
                    if (!right) {
                        return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_ERROR, "分层规则有误，请检查");
                    }
                } catch (Exception e) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_ERROR, "分层规则有误，请检查");
                }
            }
        } else {
            // 分层方式：按比例
            if (XpdLevelTypeEnum.byRatio(xpdRule.getLevelType())) {
                return CommonValidator.sumPercent(levelRuleList, "levelValue", ExceptionKeys.XPD_RULE_LEVEL_PERCENT_SUM_ERROR, "人员之和必须为100%");
            }

            // 分层方式：按固定值
            for (int i = 1; i < levelRuleList.size(); i++) {
                if (levelRuleList.get(i).getLevelValue().compareTo(levelRuleList.get(i - 1).getLevelValue()) >= 0) {
                    return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_VALUE_DECREASE_ERROR, "分层规则的值必须递减");
                }
            }
            // 最后一个输入框必须是0，若不是则高亮并提示“最低分层分值必须为0”
            BigDecimal lastLevelValue = levelRuleList.get(levelRuleList.size() - 1).getLevelValue();
            if (BigDecimal.ZERO.compareTo(lastLevelValue) != 0) {
                return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_RULE_LEVEL_VALUE_MIN_ZERO_ERROR, "最低分层分值必须为0");
            }

            if (XpdResultTypeEnum.byScore(xpdRule.getResultType()) && XpdLevelTypeEnum.byFixedValue(xpdRule.getLevelType())) {
                List<LevelBase> levelBases = BeanCopierUtil.convertList(levelRuleList, XpdLevelPO.class, LevelBase.class);
                return CommonValidator.checkLevelValue(levelBases, maxValue);
            }
        }
        return null;
    }

    private List<XpdLevelPO> convert2XpdLevelPo(String orgId, String xpdId, List<XpdLevelRuleDto> levelRuleList) {
        // 校验分层规则
        RuleMainBase ruleMainBase = new RuleMainBase();
        ruleMainBase.setOrgId(orgId);
        ruleMainBase.setBizId(xpdId);
        return BeanCopierUtil.convertList(levelRuleList, levelRule -> {
            XpdLevelPO xpdLevel = new XpdLevelPO();
            BeanCopierUtil.copy(levelRule, xpdLevel);
            xpdLevel.setId(levelRule.getLevelId());
            xpdLevel.setFormula(JSON.toJSONString(levelRule.getSpRuleBean()));
            xpdLevel.setFormulaDisplay(spRuleService.calcRuleDisplay(ruleMainBase, levelRule.getSpRuleBean()));
            return xpdLevel;
        });
    }

    public ErrorInfo validateRefIds(String orgId, XpdPO xpd, List<XpdRuleCalcIndicatorPO> cals) {
        // 校验数据来源
        List<IndicatorBase> indicatorBases = BeanCopierUtil.convertList(cals, XpdRuleCalcIndicatorPO.class, IndicatorBase.class);
        if (CollectionUtils.isEmpty(indicatorBases)) {
            return new ErrorInfo(RuleErrorCodeEnum.EMPTY.getCode(), ExceptionKeys.XPD_RULE_CALC_EMPTY, "暂未配置计算规则");
        }
        // 计算规则中指标数据来源为空，提示“指标数据来源为空，请检查”
        ErrorInfo errorInfo = CommonValidator.checkIndicatorRefIdsEmpty(indicatorBases);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }
        return xpdDimRuleValidator.validateRefIds(orgId, xpd.getAomPrjId(), indicatorBases);
    }

    public ErrorInfo validateFormula(String orgId, XpdPO xpd, String formula) {
        if (StringUtils.isEmpty(formula)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_DIM_RULE_FORMULA_EMPTY, "高级公式未配置");
        }
        // 校验高级公式的合法性
        ErrorInfo errorInfo = CommonValidator.checkXpdFormula(formula);
        if (Objects.nonNull(errorInfo)) {
            return errorInfo;
        }
        return xpdDimRuleValidator.checkFormula(orgId, xpd.getAomPrjId(), formula);
    }
}
