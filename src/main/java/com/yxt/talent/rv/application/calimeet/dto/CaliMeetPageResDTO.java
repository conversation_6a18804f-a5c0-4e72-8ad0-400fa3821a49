package com.yxt.talent.rv.application.calimeet.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/29
 */
@Data
public class CaliMeetPageResDTO {

    private String id;

    private String meetName;

    private Integer meetStatus;

    private int caliUserCount;

    private Integer caliType;

    private Integer caliMode;

    private LocalDateTime mtStartTime;

    private LocalDateTime mtEndTime;

    private List<String> organizeUserIds;

    private List<String> caliUserIds;

    private String createUserId;

    private LocalDateTime createTime;

    private String meetRecordId;

    private Integer showRateControl;

}
