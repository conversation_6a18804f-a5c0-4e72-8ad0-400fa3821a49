package com.yxt.talent.rv.application.calimeet.dto;

import java.lang.String;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4ReqDTO;
import java.util.List;
import com.yxt.modelhub.api.bean.dto.AmImpTplFieldDTO;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
@Getter
@Setter
@Schema(description = "校准人员列表导入")
public class RvCalibrationPerson4Import implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="名称", requiredMode = Schema.RequiredMode.REQUIRED )
    private String name;
    @Schema(description="父实体id;子实体选择的父实体id或者引用对象的实体id", requiredMode = Schema.RequiredMode.REQUIRED )
    private String calibrationId;
    @Schema(description="被校准人员")
    private List<AmSlDrawer4ReqDTO> userId;
    @Schema(description="校准状态")
    private String caliStatus;
    @Schema(description="人员状态")
    private String status;
    @Schema(description="最近一次校准记录")
    private List<AmSlDrawer4ReqDTO> recordId;
    @Schema(description="文件id", requiredMode = Schema.RequiredMode.REQUIRED )
    private String fileId;
    @Schema(description="导入字段", requiredMode = Schema.RequiredMode.REQUIRED )
    private List<AmImpTplFieldDTO> fields;

}
