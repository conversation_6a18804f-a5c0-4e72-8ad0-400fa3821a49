package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserIndicatorPO;
import com.yxt.talent.rv.infrastructure.repository.xpd.bean.XpdIndicatorResultDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.*;

@Data
public class CaliUserResultContainer {
    private String userId;
    @Schema(description = "发展建议")
    private String suggestion;
    @Schema(description = "校准原因")
    private String reason;
    /**
     * 校准值
     */
    private List<CaliUpdateUserResultReq> resultList;
    /**
     * 计算结果
     */
    private CaliDimResultResp calcResult;

    //计算前预先查询的数据
    private Set<String> aomDoneRefIds = new HashSet<>();
    //key refType
    private Map<Integer, List<XpdIndicatorResultDto>> refIndResultMap = new HashMap<>();
    private List<CalimeetResultUserIndicatorPO> xpdIndicatorResults = new ArrayList<>();
    private List<CalimeetResultUserDimPO> xpdDimResults = new ArrayList<>();
}
