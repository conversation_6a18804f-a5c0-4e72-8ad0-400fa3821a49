package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Data
public class XpdDimRule4Cali {
    //没有规则，只有结果
    public static final int TYPE_NONE = 0;
    public static final int TYPE_FORMULA = 1;
    public static final int TYPE_PERF = 2;
    public static final int TYPE_BUILD_FORMULA = 3;

    private int caliCalcType;
    private String sdDimId;
    private String perfSdIndicatorId;
    private String perfAomActId;
    private String calcFormula;
    private Set<String> formulaSdIndicatorSet;

    /**
     * 计算方式:0-按子维度结果计算 1-按指标结果计算 2-按绩效指标计算 3-按绩效得分计算
     */
    private Integer calcType;

    /**
     * 结果类型:0-分值 1-达标率,非<绩效维度>下有效
     */
    private Integer resultType;
    /**
     * 分层方式:0-按比例 1-按固定值
     */
    private Integer levelType;
    /**
     * 分层优先级:0-高等级优先 1-低等级优先,分层方式为<按比例>时有效
     */
    private Integer levelPriority;
    /**
     * 分层规则,json数组, 涉及到的维度分层规则id取自rv_xpd_grid_level.id
     */
    private List<DimGridLevelRuleDTO> gridLevelRules;
    private RatioLevelThresholdDto ruleThresholdDto;
    private BigDecimal totalScore;
    //原始计算的分数
    private BigDecimal calcTotalScore;
}
